console.log('pay')
class Pay {
  static isPaying = false;
  constructor() {
    console.log('pay初始化')
  }
  getFirstProductStatus() {
    fetch(API_BASE_URL + '/product/getProductFirstPayInfo?account=' + localStorage.getItem('account_num'), {
      method: 'get',
      headers: {
        'Content-Type': 'application/json'
      }
    }).then((res) => {
      return res.json()
    }).then((res) => {
      console.log(res, 'datadata')
      if(res.code === 200) {
        window.firstProductStatus = res.data.map(item=>{
          return {
            product_id: item.id,
            is_product_first_pay: item.productFirstCharge
          }
        })
        window.checkFirstProductStatus = (product_id) => {
          return window.firstProductStatus.find(item => item.product_id === String(product_id)).is_product_first_pay === 1
        }
        window?.updateLingyuView()
      }
    }).catch((err) => {
      console.log(err)
    })
  }
  async pay(product_id) {
    if (this.isPaying) {
      return
    }
    this.isPaying = true
    console.log('pay', product_id)
    const res = await fetch(API_BASE_URL + '/order/createOrderId?productId=' + product_id + '&userId=' + localStorage.getItem('account_num'), {
      method: 'get',
      headers: {
        'Content-Type': 'application/json'
      }
    }).then((res) => {
      return res.json()
    }).catch((err) => {
      console.log(err)
    })
    console.log('pay', res)
    if (res.code === 200) {
      logReport('ugd_purchase_should', {
        product_id,
        price: res.data[0].price
      })
      return new Promise((resolve, reject) => {
        if (window.flutter_inappwebview) {
          if(window.systemInfo?.osName?.toLowerCase() === 'ios') {
            let appleRes = {}
            let appleErr = ''
            window.flutterObj.applePay(res.data[0].googleProductId, res.data[0].orderId).then(async (res) => {
              console.log(res, 'appleRes')
              appleRes = res
            }).catch((err) => {
              appleErr = err
              reject(err)
            }).finally(async () => {
              this.isPaying = false
              if(appleErr) {
                logReport('ugd_purchase', {
                  product_id,
                  item_id: res.data[0].orderId,
                  purchase_state: false,
                })
                logReport('ugd_purchase_fail', {
                  fail_reason: appleErr.toString() === '5' ? '取消支付' : appleErr
                })
                return
              }
              const res1 = await fetch(API_BASE_URL + '/product/iosPay', {
                method: 'post',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  transactionId: appleRes.transactionIdentifier,
                  orderId: res.data[0].orderId,
                  deviceId: window.systemInfo?.deviceId
                })
              }).then((resLog) => {
                return resLog.json()
              }).catch((err) => {
                console.log(err)
              })
              console.log(res1, 'res1')
              if (String(res1.code) === '200') {
                logReport('ugd_purchase', {
                  product_id,
                  item_id: res.data[0].orderId,
                  price: res1.data.price,
                  purchase_state: true,
                  extra_reward: res1.data.productFirstCharge,
                  first_pay: res1.data.gameFirstCharge
                })
                logReport('ugd_purchase_suc')
                this.getFirstProductStatus()
              } else {
                logReport('ugd_purchase_fail', {
                  fail_reason: appleErr || `支付成功但${res1.message+res1.code}`
                })
                logReport('ugd_purchase', {
                  product_id,
                  item_id: res.data[0].orderId,
                  price: res1.data.price,
                  purchase_state: false,
                  extra_reward: res1.data.productFirstCharge,
                  first_pay: res1.data.gameFirstCharge
                })
              }
            })
          } else {
            let googleRes = ''
            let googleErr = ''
            window.flutterObj.googlePay(res.data[0].googleProductId, res.data[0].orderId).then(async (res) => {
              console.log(res, 'googleRes')
              googleRes = res
            }).catch((err) => {
              googleErr = err
              reject(err)
            }).finally(async () => {
              this.isPaying = false
              if(googleErr) {
                logReport('ugd_purchase', {
                  product_id,
                  item_id: res.data[0].orderId,
                  purchase_state: false,
                })
                logReport('ugd_purchase_fail', {
                  fail_reason: googleErr.toString() === '5' ? '取消支付' : googleErr
                })
                return
              }
              const res1 = await fetch(API_BASE_URL + '/product/googlePay', {
                method: 'post',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  packageCode: 'com.ol.fishstorm.survival.io',
                  productCode: res.data[0].googleProductId,
                  token: googleRes,
                  deviceId: window.systemInfo?.deviceId
                })
              }).then((resLog) => {
                return resLog.json()
              }).catch((err) => {
                console.log(err)
              })
              console.log(res1, 'res1')
              if (String(res1.code) === '200') {
                logReport('ugd_purchase', {
                  product_id,
                  item_id: res.data[0].orderId,
                  price: res1.data.price,
                  purchase_state: true,
                  extra_reward: res1.data.productFirstCharge,
                  first_pay: res1.data.gameFirstCharge
                })
                logReport('ugd_purchase_suc')
                this.getFirstProductStatus()
              } else {
                logReport('ugd_purchase', {
                  product_id,
                  item_id: res.data[0].orderId,
                  price: res1.data.price,
                  purchase_state: false,
                  extra_reward: res1.data.productFirstCharge,
                  first_pay: res1.data.gameFirstCharge
                })
                logReport('ugd_purchase_fail', {
                  fail_reason: googleErr || `谷歌支付成功但${res1.message+res1.code}`
                })
              }
            })
          }
        } else {
          resolve(true)
        }
      })
    } else {
      this.isPaying = false
      return Promise.reject(res)
    }
  }
  async payGift(product_id, list) {
    console.log(list)
    if (this.isPaying) {
      return
    }
    this.isPaying = true
    console.log('pay', product_id)
    const res = await fetch(API_BASE_URL + '/order/createOrderId?productId=' + product_id + '&userId=' + localStorage.getItem('account_num'), {
      method: 'get',
      headers: {
        'Content-Type': 'application/json'
      }
    }).then((res) => {
      return res.json()
    }).catch((err) => {
      console.log(err)
    })
    console.log('pay', res)
    if (res.code === 200) {
      logReport('ugd_purchase_should', {
        product_id,
        price: res.data[0].price
      })
      return new Promise((resolve, reject) => {
        if (window.flutter_inappwebview) {
          if(window.systemInfo?.osName?.toLowerCase() === 'ios') {
            let appleRes = {}
            let appleErr = ''
            window.flutterObj.applePay(res.data[0].googleProductId, res.data[0].orderId).then(async (res) => {
              console.log(res, 'appleRes')
              appleRes = res
            }).catch((err) => {
              appleErr = err
              reject(err)
            }).finally(async () => {
              this.isPaying = false
              if(appleErr) {
                logReport('ugd_purchase', {
                  product_id,
                  item_id: res.data[0].orderId,
                  purchase_state: false,
                })
                logReport('ugd_purchase_fail', {
                  fail_reason: appleErr.toString() === '5' ? '取消支付' : appleErr
                })
                return
              }
              const res1 = await fetch(API_BASE_URL + '/product/iosBuyGift', {
                method: 'post',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  transactionId: appleRes.transactionIdentifier,
                  orderId: res.data[0].orderId,
                  deviceId: window.systemInfo?.deviceId,
                  list
                })
              }).then((resLog) => {
                return resLog.json()
              }).catch((err) => {
                console.log(err)
              })
              console.log(res1, 'res1')
              if (String(res1.code) === '200') {
                logReport('ugd_purchase', {
                  product_id,
                  item_id: res.data[0].orderId,
                  price: res1.data.price,
                  purchase_state: true,
                  extra_reward: res1.data.productFirstCharge,
                  first_pay: res1.data.gameFirstCharge
                })
                logReport('ugd_purchase_suc')
                this.getFirstProductStatus()
              } else {
                logReport('ugd_purchase_fail', {
                  fail_reason: appleErr || `支付成功但${res1.message+res1.code}`
                })
                logReport('ugd_purchase', {
                  product_id,
                  item_id: res.data[0].orderId,
                  price: res1.data.price,
                  purchase_state: false,
                  extra_reward: res1.data.productFirstCharge,
                  first_pay: res1.data.gameFirstCharge
                })
              }
            })
          } else {
            let googleRes = ''
            let googleErr = ''
            window.flutterObj.googlePay(res.data[0].googleProductId, res.data[0].orderId).then(async (res) => {
              console.log(res, 'googleRes')
              googleRes = res
            }).catch((err) => {
              googleErr = err
              reject(err)
            }).finally(async () => {
              this.isPaying = false
              if(googleErr) {
                logReport('ugd_purchase', {
                  product_id,
                  item_id: res.data[0].orderId,
                  purchase_state: false,
                })
                logReport('ugd_purchase_fail', {
                  fail_reason: googleErr.toString() === '5' ? '取消支付' : googleErr
                })
                return
              }
              const res1 = await fetch(API_BASE_URL + '/product/googleBuyGift', {
                method: 'post',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  packageCode: 'com.ol.fishstorm.survival.io',
                  productCode: res.data[0].googleProductId,
                  token: googleRes,
                  deviceId: window.systemInfo?.deviceId,
                  list
                })
              }).then((resLog) => {
                return resLog.json()
              }).catch((err) => {
                console.log(err)
              })
              console.log(res1, 'res1')
              if (String(res1.code) === '200') {
                logReport('ugd_purchase', {
                  product_id,
                  item_id: res.data[0].orderId,
                  price: res1.data.price,
                  purchase_state: true,
                  extra_reward: res1.data.productFirstCharge,
                  first_pay: res1.data.gameFirstCharge
                })
                logReport('ugd_purchase_suc')
                this.getFirstProductStatus()
              } else {
                logReport('ugd_purchase', {
                  product_id,
                  item_id: res.data[0].orderId,
                  price: res1.data.price,
                  purchase_state: false,
                  extra_reward: res1.data.productFirstCharge,
                  first_pay: res1.data.gameFirstCharge
                })
                logReport('ugd_purchase_fail', {
                  fail_reason: googleErr || `谷歌支付成功但${res1.message+res1.code}`
                })
              }
            })
          }
        } else {
          resolve(true)
        }
      })
    } else {
      this.isPaying = false
      return Promise.reject(res)
    }
  }
}

window.payObj = new Pay()