<?php

// error_reporting(0);

$usr = $_POST['usr'];
$bao = '0';
$num = $_POST['num'];
$item = '';
$type = $_POST['type'];
$gnxz = '2';

$usr = str_replace(array(' ','%'),'',$usr);
$gnxz = str_replace(array(' ','%'),'',$gnxz);
$usr = str_replace(array(' ','%'),'',$usr);
$type = str_replace(array(' ','%'),'',$type);
$usr =='' && (die('请输入内容'));
$gnxz == 0 && (die('请选择功能'));
include "config.php";
$mysql = mysqli_connect($PZ['DB_HOST'],$PZ['DB_USER'],$PZ['DB_PWD'],$PZ['DB_NAME'],$PZ['DB_PORT']) or die("数据库连接错误");
$mysql->query('set names utf8');
$xx = mysqli_fetch_assoc($mysql->query("SELECT * FROM tafang_center.account WHERE account = '$usr' limit 1"));
$xx['id'] =='' && (die('无此角色'));
$rid = $xx['id'];
$ss = mysqli_fetch_assoc($mysql->query("SELECT pass,type FROM cdk WHERE uid = '$rid' limit 1"));
//$ss['type'] < $gnxz && (die('此角色未授权此功能'));
//$ss['pass'] != $sqm && (die('后台密码错误'));
$num>********* && (die('数量限制1-9999'));


if($gnxz==1){
    $bao == 0 && (die('请选择充值项目'));
    $bao > 15 && (die('错误的充值项目'));
	$data = urlencode($bao);
// 	$re = gmmail(8800 + $rid,$item,$itemnum); 
	exit("");
// 	$ret = json_decode(curl_https("http://127.0.0.1:7555/GM?cmd=openServer&id={$rid}&type={$data}"),true);
// 	if($ret['success']==1){die('发送成功');}else{die($ret['reason']);}
}elseif($gnxz==2){
    $item =='0' && (die('请选择物品'));
	$find=false;

	$json_string = file_get_contents('./item.json');
    $data = json_decode($json_string, true);
    foreach($data as $txts){
        if(trim($txts['resId']) == trim($item)){
			$find=true;
			break;
		}
    }
	//if($find==false){die('此物品您无权发送');}
	$num =='' && ($num = 1); 
	$ts = time();
	$title = urlencode('系统邮件');
	$txt = urlencode('您有新的邮件,请及时查收');
// 	if($item > 99){$num > 100 && ($num = 100);}else{$num > 99999999 && ($num = 99999999);}
$xx222 = mysqli_fetch_assoc($mysql->query("SELECT * FROM  tafang_game_zjy.role WHERE code = '$usr' limit 1"));
$xx222['id'] =='' && (die('无此角色'));
$uid = $xx222['id'];
	$re = gmmail($uid,$type,$num); 
	exit($re);
// 	$ret = json_decode(curl_https("http://127.0.0.1:7555/GM?cmd=sendMail&names={$rid}&sn={$item}&num={$num}&title={$title}&detail={$txt}&batchCode={$ts}&expiredTime=7"),true);;
// 	if($ret['success']==1){die('发送成功');}else{die('发送失败');}
}else{
    die('error');
}

function curl_https($url){
	$ch = curl_init(); 
	curl_setopt($ch, CURLOPT_URL, $url); 
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
	curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER,true); 
	$cnt = curl_exec($ch);
	curl_close($ch); 
    return $cnt; 
}

function curl_http_post($url,$data){
	$ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: application/json, text/plain, */*',
        'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Connection: keep-alive',
        'Content-Type: application/json',
        'User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********',
        'Accept-Encoding: gzip',
        'X-Mgip-sign: gzip',
    ]);
   
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    
    $response = curl_exec($ch);
    
    curl_close($ch);
    return $response;
}
function gmmail($uuid,$type,$itemnum){
    
     
    $url = 'http://127.0.0.1:19000/center/gm';
    
    // $param=array(
    //     "role"=>$uuid,
    //     "item"=>[array($item,$itemnum)
    //       ]
    //     );
    
    $param=array(
        "role"=>$uuid,
        "type"=>$type,
        "amount"=>$itemnum
    );
        
    $data = json_encode($param);
    
    $response = curl_http_post($url,$data);

    
        return '发送成功';


}

  

?>