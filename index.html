<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">

  <title>app</title>

  <!--http://www.html5rocks.com/en/mobile/mobifying/-->
  <meta name="viewport"
        content="width=device-width,user-scalable=no,initial-scale=1, minimum-scale=1,maximum-scale=1"/>

  <!--https://developer.apple.com/library/safari/documentation/AppleApplications/Reference/SafariHTMLRef/Articles/MetaTags.html-->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="format-detection" content="telephone=no">

  <!-- force webkit on 360 -->
  <meta name="renderer" content="webkit"/>
  <meta name="force-rendering" content="webkit"/>
  <!-- force edge on IE -->
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
  <meta name="msapplication-tap-highlight" content="no">

  <!-- force full screen on some browser -->
  <meta name="full-screen" content="yes"/>
  <meta name="x5-fullscreen" content="true"/>
  <meta name="360-fullscreen" content="true"/>

  <!-- force screen orientation on some browser -->

  <!--fix fireball/issues/3568 -->
  <!--<meta name="browsermode" content="application">-->
  <meta name="x5-page-mode" content="app">

  <!--<link rel="apple-touch-icon" href=".png" />-->
  <!--<link rel="apple-touch-icon-precomposed" href=".png" />-->

  <link rel="stylesheet" type="text/css" href="style-mobile.25fc5.css"/>
  <link rel="icon" href="favicon.8de18.ico"/>
</head>

<body>
  <canvas id="GameCanvas" oncontextmenu="event.preventDefault()" tabindex="0"></canvas>
  <div id="splash">
    <!-- <div class="progress-bar stripes">
      <span style="width: 0%"></span>
    </div> -->
  </div>

<!--  ======================================== TY:游戏工具 ============================================-->
<div id="game-controls" class="game-controls">
    <div class="main-button" id="control-handle">
      <svg t="1742800854072" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2823" width="200" height="200"><path d="M512 330.688a181.312 181.312 0 1 0 0 362.624 181.312 181.312 0 0 0 0-362.624zM394.688 512a117.312 117.312 0 1 1 234.624 0 117.312 117.312 0 0 1-234.624 0z" fill="#333333" p-id="2824"></path><path d="M596.224 96H427.776c-32.896 0-59.84 0-82.048 2.176-23.232 2.304-43.712 7.168-63.104 18.304-19.392 11.136-33.92 26.432-47.488 45.376-13.056 18.048-26.56 41.344-43.008 69.76L108.48 375.808c-16.512 28.48-30.08 51.84-39.36 72.192-9.728 21.312-15.808 41.6-15.808 64s6.08 42.688 15.808 64c9.216 20.352 22.848 43.712 39.36 72.192l83.648 144.192c16.448 28.416 29.952 51.712 43.008 69.76 13.632 18.944 28.096 34.24 47.488 45.44 19.392 11.136 39.872 16 63.104 18.24 22.208 2.176 49.152 2.176 82.048 2.176h168.448c32.896 0 59.84 0 82.048-2.176 23.232-2.304 43.712-7.104 63.104-18.304 19.392-11.136 33.92-26.432 47.488-45.376 13.056-18.048 26.56-41.344 43.008-69.76l83.648-144.192c16.512-28.48 30.08-51.84 39.36-72.192 9.728-21.312 15.808-41.6 15.808-64s-6.08-42.688-15.808-64c-9.28-20.352-22.848-43.712-39.36-72.192l-83.648-144.192c-16.448-28.416-29.952-51.712-43.008-69.76-13.632-18.944-28.096-34.24-47.488-45.44-19.392-11.072-39.872-16-63.104-18.24C656 96 629.12 96 596.224 96zM314.56 171.968c8.768-5.12 19.456-8.32 37.44-10.112 18.688-1.792 42.496-1.856 77.312-1.856h165.376c34.816 0 58.624 0 77.312 1.92 17.984 1.728 28.672 4.992 37.44 10.048 8.768 5.056 16.96 12.608 27.52 27.264 10.944 15.232 22.912 35.776 40.32 65.856l82.112 141.44c17.536 30.208 29.44 50.88 37.248 67.968 7.488 16.512 10.048 27.392 10.048 37.504 0 10.112-2.56 20.992-10.048 37.504-7.808 17.088-19.712 37.76-37.248 67.968l-82.112 141.44c-17.408 30.08-29.376 50.624-40.32 65.856-10.56 14.656-18.752 22.272-27.52 27.264-8.768 5.12-19.456 8.32-37.44 10.112-18.688 1.792-42.496 1.856-77.312 1.856H429.312c-34.816 0-58.624 0-77.312-1.92-17.984-1.728-28.672-4.992-37.44-10.048-8.768-5.056-16.96-12.608-27.52-27.264-10.944-15.232-22.912-35.776-40.32-65.856l-82.112-141.44c-17.536-30.208-29.44-50.88-37.248-67.968-7.488-16.512-10.048-27.392-10.048-37.504 0-10.112 2.56-20.992 10.048-37.504 7.808-17.088 19.712-37.76 37.248-67.968l82.112-141.44c17.408-30.08 29.376-50.624 40.32-65.856 10.56-14.656 18.752-22.208 27.52-27.264z" fill="#333333" p-id="2825"></path></svg>    </div>
    <div class="game-menu">
      <div class="menu-header">
        <span class="menu-title">游戏工具</span>
        <button class="lang-btn" style="margin-right: 8px; margin-left: 8px; font-size: 15px; background: linear-gradient(to bottom, #ffd700, #ffb700); color: #333; border: none; border-radius: 6px; padding: 2px 12px; cursor: pointer; box-shadow: 0 1px 2px rgba(0,0,0,0.08); font-weight: bold; transition: background 0.2s;">ไทย</button>
        <button class="close-btn">&times;</button>
      </div>
      <div class="menu-tabs">
        <button class="tab-btn active" data-tab="speed">倍速</button>
        <button class="tab-btn" data-tab="auto">外挂</button>
        <button class="tab-btn" data-tab="debug">调试</button>
      </div>
      <div class="tab-content">
        <div class="tab-pane active" id="speed-tab">
          <div class="option-title">游戏倍速</div>
          <div class="option-buttons" id="speed-btns">
            <button data-speed="1">1x</button>
            <button data-speed="5">5x</button>
            <button data-speed="10">10x</button>
            <button data-speed="20">20x</button>
          </div>
          <div class="option-title" style="margin-top: 15px;">战斗控制</div>
          <div class="option-buttons battle-buttons">
            <button id="skip-battle" class="battle-button">跳过战斗</button>
            <button id="auto-battle" class="battle-button">开启自动战斗</button>
            <div>tips:跳过战斗无法获取与正常战斗相同的物资奖励</div>
            <div>tips:开启自动战斗后会触发自动点击事件，请开启后立刻进入战斗；若手动暂停退出战斗，需要手动关闭自动战斗，胜利或失败会自动关闭</div>
          </div>
        </div>
        <div class="tab-pane" id="auto-tab">
          <div class="option-title">获取物品</div>
          <div class="item-controls">
            <select id="item-select" class="game-select">
              <option value="3">经验</option>
              <option value="6">钻石</option>
              <option value="7">金币</option>
              <option value="8">蚌壳</option>
              <option value="9">鱼饵</option>
              <option value="10">贝壳</option>
              <option value="10002">渔夫帽升级图纸</option>
              <option value="10003">钓服升级图纸</option>
              <option value="10004">幸运符升级图纸</option>
              <option value="10005">手套升级图纸</option>
              <option value="10006">渔靴升级图纸</option>
              <option value="10007">新手道书</option>
              <option value="10008">深海矿石</option>
              <option value="10009">珊瑚化石</option>
              <option value="10010">神秘羊皮卷轴</option>
              <option value="10011">随机装备图纸</option>
              <option value="10012">随机技能卷轴</option>
              <option value="10013">随机珍珠箱子</option>
              <option value="10014">普通珍珠箱</option>
              <option value="10015">璀璨珍珠箱</option>
              <option value="10016">随机紫色珍珠宝箱</option>
              <option value="10017">随机金色珍珠宝箱</option>
              <option value="10018">随机红色珍珠宝箱</option>
              <option value="10019">随机多彩珍珠宝箱</option>
              <option value="10020">珍珠抽取券</option>
              <option value="10021">协战宠物抽取券</option>
              <option value="10022">随机珍珠箱子</option>
              <option value="10023">随机稀有珍珠</option>
              <option value="10024">随机珍珠箱子</option>
              <option value="10101">鱼叉投掷卷轴</option>
              <option value="10102">鱼雷卷轴</option>
              <option value="10103">液氮冷冻弹卷轴</option>
              <option value="10104">电磁脉冲卷轴</option>
              <option value="10105">拖网卷轴</option>
              <option value="10106">高压水枪卷轴</option>
              <option value="10107">鱼枪连射卷轴</option>
              <option value="10108">寒冰陷阱卷轴</option>
              <option value="10109">连锁闪电卷轴</option>
              <option value="10110">渔网天降卷轴</option>
              <option value="10111">声呐漩涡卷轴</option>
              <option value="10112">电磁领域卷轴</option>
              <option value="10113">网兜陷阱卷轴</option>
              <option value="10114">声波攻击卷轴</option>
              <option value="10115">特斯拉电圈卷轴</option>
              <option value="10116">改名卡</option>
              <option value="10117">随机珍珠箱子</option>
              <option value="10118">随机蓝色珍珠宝箱</option>
              <option value="10119">宝箱钻石掉落</option>
              <option value="10120">技能卷轴自选宝箱</option>
              <option value="10121">小鱼干</option>
              <option value="10122">协战宠物抽卡箱</option>
              <option value="10123">小鱼干</option>
              <option value="10999">1元代金券</option>
            </select>
            <input type="number" id="item-quantity" class="game-input" placeholder="输入数量" min="1">
            <button id="send-item" class="game-button">获取物品</button>
          </div>
        </div>
        <div class="tab-pane" id="debug-tab">
          <div class="option-title">调试工具</div>
          <div class="debug-buttons">
            <button id="devbug-console">显示控制台</button>
            <button id="debug-clear">清除缓存</button>
            <button id="debug-reload">刷新页面</button>
            <button id="debug-lang">切换语言</button>
            <button id="debug-first-pay">显示首充礼包</button>
            <button id="debug-limited-gift">显示限时礼包</button>
            <button id="debug-ability-gift">显示能力提升礼包</button>
            <button id="debug-energy-gift">显示体力补充礼包</button>
          </div>
        </div>
      </div>
    </div>
  </div>
<!-- 多语言选择弹窗 -->
<div id="language-modal" class="language-modal">
    <div class="language-modal-content">
      <button class="language-modal-close">
        <img src="close.png" alt="关闭">
      </button>
      <div class="language-modal-header">
        <span class="language-modal-title">切换语言</span>
      </div>
      <div class="language-options">
        <div class="language-option" data-lang="zh">
          <span class="language-name">繁體中文</span>
        </div>
        <div class="language-option" data-lang="en">
          <span class="language-name">English</span>
        </div>
        <div class="language-option" data-lang="th">
          <span class="language-name">ไทย</span>
        </div>
      </div>
    </div>
  </div>

<div id="delete-account" class="delete-account">
        <div class="delete-account-content">
          <button class="delete-account-close">
            <img src="delete-close.png" alt="关闭" />
          </button>
          <div class="delete-account-header">
            <span class="delete-account-title">删除账号</span>
          </div>
          <div class="delete-account-options">
              <div class="delete-account-input-bg">
                  <input type="password" id="delete-account-input1" class="delete-account-input" placeholder="请输入密码" />
              </div>
              <div class="delete-account-input-bg">
                  <input type="password" id="delete-account-input2" class="delete-account-input" placeholder="请再次输入密码" />
              </div>
            <button id="delete-account-btn" class="delete-account-btn">删除账号</button>
          </div>
        </div>
    </div>

<div id="first-pay-gift" class="first-pay-gift">
    <div class="first-pay-gift-content">
      <button class="first-pay-gift-close">
        <img src="/gift-pic/首充礼包/png/关闭.png" alt="关闭" />
      </button>
      <div class="first-pay-gift-options">
        <div class="first-pay-gift-option">
          Lv1
        </div>
        <div class="first-pay-gift-option">
          Lv2
        </div>
        <div class="first-pay-gift-option">
          Lv3
        </div>
      </div>
      <div class="first-pay-gifts">
        <div class="first-pay-gift-item" data-item-id="1">
          <img src="/gift-pic/首充礼包/png/物品缩放/lv1.png" alt="" width="50">
        </div>
        <div class="first-pay-gift-item" data-item-id="2">
          <img src="/gift-pic/首充礼包/png/物品缩放/lv1.png" alt="" width="50">
        </div>
        <div class="first-pay-gift-item" data-item-id="3">
          <img src="/gift-pic/首充礼包/png/物品缩放/lv1.png" alt="" width="50">
        </div>
      </div>
      <div class="first-pay-text">
        紫色珍珠三选一
      </div>
      <div class="first-pay-gift-btn">
        $0.49
      </div>
    </div>
  </div>

<div id="power-gift" class="power-gift">
    <div class="power-gift-content">
      <button class="power-gift-close">
        <img src="/gift-pic/体力补充包/png/关闭.png" alt="关闭" />
      </button>
      <div class="power-gift-btn">
        $2.99
      </div>
    </div>
  </div>

<!-- 能力提升礼包弹窗 -->
<div id="ability-gift" class="ability-gift">
  <div class="ability-gift-content">
    <button class="ability-gift-close">
      <img src="/gift-pic/能力提升/png/关闭.png" alt="关闭" />
    </button>
    <div class="ability-gift-btn">
      $1.99
    </div>
  </div>
</div>


<!--  限时礼包弹窗-->
<!-- 技能类型限时礼包弹窗 -->
<div id="limited-gift-skill" class="limited-gift-skill">
  <div class="limited-gift-skill-content">
    <button class="limited-gift-skill-close">
      <img src="/gift-pic/限时礼包/png/关闭.png" alt="关闭" />
    </button>
    <div class="limited-gift-skill-tabs">
      <div class="limited-gift-skill-tab active" data-level="1">
        Lv.1
      </div>
      <div class="limited-gift-skill-tab" data-level="2">
        Lv.2
      </div>
      <div class="limited-gift-skill-tab" data-level="3">
        Lv.3
      </div>
    </div>
    <div class="limited-gift-skill-btn">
      $1.99
    </div>
  </div>
</div>

<!-- 商城类型限时礼包弹窗 -->
<div id="limited-gift-shop" class="limited-gift-shop">
  <div class="limited-gift-shop-content">
    <button class="limited-gift-shop-close">
      <img src="/gift-pic/限时礼包/png/关闭.png" alt="关闭" />
    </button>
    <div class="limited-gift-shop-tabs">
      <div class="limited-gift-shop-tab active" data-level="1">
        Lv.1
      </div>
      <div class="limited-gift-shop-tab" data-level="2">
        Lv.2
      </div>
      <div class="limited-gift-shop-tab" data-level="3">
        Lv.3
      </div>
    </div>
    <div class="limited-gift-shop-btn">
      $1.99
    </div>
  </div>
</div>

<!-- 灵宠类型限时礼包弹窗 -->
<div id="limited-gift-pet" class="limited-gift-pet">
  <div class="limited-gift-pet-content">
    <button class="limited-gift-pet-close">
      <img src="/gift-pic/限时礼包/png/关闭.png" alt="关闭" />
    </button>
    <div class="limited-gift-pet-tabs">
      <div class="limited-gift-pet-tab active" data-level="1">
        Lv.1
      </div>
      <div class="limited-gift-pet-tab" data-level="2">
        Lv.2
      </div>
      <div class="limited-gift-pet-tab" data-level="3">
        Lv.3
      </div>
    </div>
    <div class="limited-gift-pet-btn">
      $1.99
    </div>
  </div>
</div>

    <!--  ======================================== TY:游戏工具 ============================================-->

<script src="src/settings.b273e.js" charset="utf-8"></script>
<script src="hybird/config.js" charset="utf-8"></script>
<script src="hybird/emailText.js" charset="utf-8"></script>
<script src="hybird/flutter.js" charset="utf-8"></script>
<script src="hybird/ad.js" charset="utf-8"></script>
<script src="hybird/pay.js" charset="utf-8"></script>
<script src="hybird/logs.js" charset="utf-8"></script>
<script src="main.f441f.js" charset="utf-8"></script>
<!--  ======================================== TY:游戏工具 ============================================-->
<script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>
<script src="https://www.uggamer.com/uggame-page/h5sdk/h5gamesdk.js" charset="utf-8"></script>
<!--  ======================================== TY:游戏工具 ============================================-->

<script type="text/javascript">
(function () {
    // open web debugger console
    // if (typeof VConsole !== 'undefined') {
    //     window.vConsole = new VConsole();
    // }

    var debug = window._CCSettings.debug;
    var splash = document.getElementById('splash');
    if(splash){
    splash.style.display = 'block';
    }

    function loadScript (moduleName, cb) {
      function scriptLoaded () {
          document.body.removeChild(domScript);
          domScript.removeEventListener('load', scriptLoaded, false);
          cb && cb();
      }
      var domScript = document.createElement('script');
      domScript.async = true;
      domScript.src = moduleName;
      domScript.addEventListener('load', scriptLoaded, false);
      document.body.appendChild(domScript);
    }
    loadScript(debug ? 'cocos2d-js.f8dbd.js' : 'cocos2d-js.f8dbd.js', function () {
      if (CC_PHYSICS_BUILTIN || CC_PHYSICS_CANNON) {
        loadScript(debug ? 'physics.js' : 'physics-min.js', window.boot);
      }
      else {
        window.boot();
      }
    });


  // <!--  ======================================== TY:游戏工具 ============================================-->
    document.addEventListener('DOMContentLoaded', function() {


      // 物品下拉框中泰文映射表和切换函数，必须放在最前面
      const itemOptionsMap = {
        '经验': 'ประสบการณ์',
        '钻石': 'เพชร',
        '金币': 'เหรียญทอง',
        '蚌壳': 'เปลือกหอย',
        '鱼饵': 'เหยื่อปลา',
        '贝壳': 'เปลือกหอย',
        '渔夫帽升级图纸': 'แบบแปลนอัพเกรดหมวกชาวประมง',
        '钓服升级图纸': 'แบบแปลนอัพเกรดเสื้อตกปลา',
        '幸运符升级图纸': 'แบบแปลนอัพเกรดเครื่องรางโชคดี',
        '手套升级图纸': 'แบบแปลนอัพเกรดถุงมือ',
        '渔靴升级图纸': 'แบบแปลนอัพเกรดรองเท้าตกปลา',
        '新手道书': 'คู่มือผู้เริ่มต้น',
        '深海矿石': 'แร่ใต้ทะเลลึก',
        '珊瑚化石': 'ฟอสซิลปะการัง',
        '神秘羊皮卷轴': 'ม้วนหนังแกะลึกลับ',
        '随机装备图纸': 'แบบแปลนอุปกรณ์สุ่ม',
        '随机技能卷轴': 'ม้วนสกิลสุ่ม',
        '随机珍珠箱子': 'กล่องไข่มุกสุ่ม',
        '普通珍珠箱': 'กล่องไข่มุกธรรมดา',
        '璀璨珍珠箱': 'กล่องไข่มุกเจิดจ้า',
        '随机紫色珍珠宝箱': 'กล่องไข่มุกสีม่วงสุ่ม',
        '随机金色珍珠宝箱': 'กล่องไข่มุกสีทองสุ่ม',
        '随机红色珍珠宝箱': 'กล่องไข่มุกสีแดงสุ่ม',
        '随机多彩珍珠宝箱': 'กล่องไข่มุกหลากสีสุ่ม',
        '珍珠抽取券': 'ตั๋วสุ่มไข่มุก',
        '协战宠物抽取券': 'ตั๋วสุ่มเพ็ทช่วยต่อสู้',
        '随机稀有珍珠': 'ไข่มุกหายากสุ่ม',
        '鱼叉投掷卷轴': 'ม้วนการขว้างฉมวก',
        '鱼雷卷轴': 'ม้วนตอร์ปิโด',
        '液氮冷冻弹卷轴': 'ม้วนระเบิดน้ำแข็ง',
        '电磁脉冲卷轴': 'ม้วนคลื่นแม่เหล็กไฟฟ้า',
        '拖网卷轴': 'ม้วนอวนลาก',
        '高压水枪卷轴': 'ม้วนปืนฉีดน้ำแรงดันสูง',
        '鱼枪连射卷轴': 'ม้วนปืนฉมวก',
        '寒冰陷阱卷轴': 'ม้วนกับดักน้ำแข็ง',
        '连锁闪电卷轴': 'ม้วนสายฟ้าลูกโซ่',
        '渔网天降卷轴': 'ม้วนปืนน้ำแรงดัน',
        '声呐漩涡卷轴': 'ม้วนวังวนโซนาร์',
        '电磁领域卷轴': 'ม้วนเขตแม่เหล็ก',
        '网兜陷阱卷轴': 'ม้วนกับดักตาข่าย',
        '声波攻击卷轴': 'ม้วนโจมตีเสียง',
        '特斯拉电圈卷轴': 'ม้วนคอยล์เทสลา',
        '改名卡': 'การ์ดเปลี่ยนชื่อ',
        '随机蓝色珍珠宝箱': 'ไข่มุกสีน้ำเงินสุ่ม',
        '宝箱钻石掉落': 'ดรอปเพชรจากกล่อง',
        '技能卷轴自选宝箱': 'กล่องเลือกม้วนสกิล',
        '小鱼干': 'ปลาแห้ง',
        '协战宠物抽卡箱': 'กล่องการ์ดเพ็ทช่วยต่อสู้',
        '1元代金券': 'คูปอง1หยวน',
      };

      // 语言切换映射表
      const langMap = {
        zh: {
          "密码place1": "8-16 個字符",
          "密码place2": "再次確認密碼",
          "密码错误": "密碼錯誤",
          "密码不能为空": "密码不能为空",
          "两次输入的密码不一致": "兩次輸入的密碼不一致",
          "确认": "確認",
          "删除账号": "刪除帳號",
          '切换语言': '切换语言',
          '游戏工具': '游戏工具',
          '倍速': '倍速',
          '外挂': '外挂',
          '调试': '调试',
          '游戏倍速': '游戏倍速',
          '战斗控制': '战斗控制',
          '跳过战斗': '跳过战斗',
          '开启自动战斗': '开启自动战斗',
          'tips:跳过战斗无法获取与正常战斗相同的物资奖励': 'tips:跳过战斗无法获取与正常战斗相同的物资奖励',
          'tips:开启自动战斗后会触发自动点击事件，请开启后立刻进入战斗；若手动暂停退出战斗，需要手动关闭自动战斗，胜利或失败会自动关闭': 'tips:开启自动战斗后会触发自动点击事件，请开启后立刻进入战斗；若手动暂停退出战斗，需要手动关闭自动战斗，胜利或失败会自动关闭',
          '获取物品': '获取物品',
          '输入数量': '输入数量',
          '调试工具': '调试工具',
          '显示控制台': '显示控制台',
          '清除缓存': '清除缓存',
          '未找到账号信息': '未找到账号信息',
          '请输入有效的数量': '请输入有效的数量',
          '物品发送成功！请在信件中领取': '物品发送成功！请在信件中领取',
          '发送失败': '发送失败',
          '发送失败，请检查网络连接': '发送失败，请检查网络连接',
        },
        th: {
          "密码place1": "8-16 ตัวอักษร",
          "密码place2": "ยืนยันรหัสผ่านอีกครั้ง",
          "密码错误": "รหัสผ่านผิด",
          "密码不能为空": "กรุณากรอ",
          "两次输入的密码不一致": "รหัสผ่านที่กรอกทั้งสองครั้งไม่ตรงกัน",
          "确认": "ยืนยัน",
          "删除账号": "ลบบัญชี",
          '切换语言': 'เปลี่ยนภาษา',
          '游戏工具': 'เครื่องมือเกม',
          '倍速': 'เร่งความเร็ว',
          '外挂': 'โปรแกรมช่วย',
          '调试': 'ดีบัก',
          '游戏倍速': 'ความเร็วเกม',
          '战斗控制': 'ควบคุมการต่อสู้',
          '跳过战斗': 'ข้ามการต่อสู้',
          '开启自动战斗': 'เปิดการต่อสู้อัตโนมัติ',
          'tips:跳过战斗无法获取与正常战斗相同的物资奖励': 'เคล็ดลับ: การข้ามการต่อสู้จะไม่ได้รับรางวัลเหมือนการต่อสู้ปกติ',
          'tips:开启自动战斗后会触发自动点击事件，请开启后立刻进入战斗；若手动暂停退出战斗，需要手动关闭自动战斗，胜利或失败会自动关闭': 'หมายเหตุ: เมื่อเปิดออโต้แบทเทิลจะมีการคลิกอัตโนมัติ กรุณาเข้าสู่การต่อสู้ทันที หากหยุดชั่วคราวหรือออกจากการต่อสู้ ต้องปิดออโต้แบทเทิลเอง เมื่อชนะหรือแพ้จะปิดออโต้แบทเทิลโดยอัตโนมัติ',
          '获取物品': 'รับไอเทม',
          '输入数量': 'ป้อนจำนวน',
          '调试工具': 'เครื่องมือดีบัก',
          '显示控制台': 'แสดงคอนโซล',
          '清除缓存': 'ล้างแคช',
          '未找到账号信息': 'ไม่พบข้อมูลบัญชี',
          '请输入有效的数量': 'กรุณากรอกจำนวนที่ถูกต้อง',
          '物品发送成功！请在信件中领取': 'ส่งไอเทมสำเร็จ! กรุณารับในจดหมาย',
          '发送失败': 'ส่งไม่สำเร็จ',
          '发送失败，请检查网络连接': 'ส่งไม่สำเร็จ กรุณาตรวจสอบการเชื่อมต่อเครือข่าย',
        },
        en: {
          '切换语言': 'Switch Language',
          "密码place1": "8~16 characters",
          "密码place2": "Confirm password again",
          "密码错误": "Incorrect password",
          "密码不能为空": "Password is required",
          "两次输入的密码不一致": "Passwords do not match",
          "确认": "Confirm",
          "删除账号": "Delete Account",
        }
      };
      const controlsPanel = document.getElementById('game-controls');
      if(!window.location.hostname.includes('dreampower.hk')) {
        controlsPanel.style.display = 'block';
      }
      const controlHandle = document.getElementById('control-handle');
      const gameMenu = document.querySelector('.game-menu');
      const closeBtn = document.querySelector('.close-btn');
      const tabBtns = document.querySelectorAll('.tab-btn');
      const tabPanes = document.querySelectorAll('.tab-pane');
      const speedBtns = document.querySelectorAll('#speed-btns button');

      // 调试信息
      console.log('游戏控制面板初始化');
      console.log('controlHandle:', controlHandle);
      console.log('gameMenu:', gameMenu);

      // 当前游戏速度
      let currentSpeed = localStorage.getItem('gameSpeed') || 1;

      // 拖动功能变量
      let isDragging = false;
      let dragOffsetX = 0;
      let dragOffsetY = 0;
      let dragThreshold = 5; // 添加拖动阈值，小于此距离视为点击而非拖动
      let dragStartX = 0;
      let dragStartY = 0;

      // 切换菜单显示
      function toggleMenu() {
        console.log('触发toggleMenu, 当前状态:', gameMenu.classList.contains('visible'));
        gameMenu.classList.toggle('visible');
      }

      // 设置主按钮点击
      controlHandle.addEventListener('click', function(e) {
        console.log('主按钮点击');
        // 检查是否是拖动结束后的点击
        if (isDragging) {
          console.log('拖动结束后的点击，不处理');
          return;
        }

        // 计算移动距离
        const moveDistance = Math.sqrt(
                Math.pow(e.clientX - dragStartX, 2) +
                Math.pow(e.clientY - dragStartY, 2)
        );

        // 如果移动距离小于阈值，视为点击而不是拖动
        if (moveDistance < dragThreshold) {
          console.log('打开菜单');
          toggleMenu();
          e.stopPropagation();
        }
      });

      // 拖动功能
      function setupDragFunctionality() {
        // 开始拖动
        controlHandle.addEventListener('mousedown', function(e) {
          // 记录开始位置用于计算移动距离
          dragStartX = e.clientX;
          dragStartY = e.clientY;

          // 检查是右键点击还是左键点击，右键点击打开菜单
          if (e.button === 2) {
            toggleMenu();
            return;
          }

          // 左键点击进入拖动模式
          const rect = controlsPanel.getBoundingClientRect();
          isDragging = true;
          dragOffsetX = e.clientX - rect.left;
          dragOffsetY = e.clientY - rect.top;
          controlHandle.style.cursor = 'grabbing';

          // 阻止浏览器默认拖动行为
          e.preventDefault();
        });

        // 触摸开始(移动设备)
        controlHandle.addEventListener('touchstart', function(e) {
          const touch = e.touches[0];
          // 记录开始位置用于计算移动距离
          dragStartX = touch.clientX;
          dragStartY = touch.clientY;

          const rect = controlsPanel.getBoundingClientRect();
          isDragging = true;
          dragOffsetX = touch.clientX - rect.left;
          dragOffsetY = touch.clientY - rect.top;

          // 阻止滚动和其他默认行为
          e.preventDefault();
        });

        // 移动
        document.addEventListener('mousemove', function(e) {
          if (!isDragging) return;

          let left = e.clientX - dragOffsetX;
          let top = e.clientY - dragOffsetY;

          // 边界检查
          const windowWidth = window.innerWidth;
          const windowHeight = window.innerHeight;

          // 确保不超出屏幕
          left = Math.max(0, Math.min(left, windowWidth - 70));
          top = Math.max(0, Math.min(top, windowHeight - 70));

          // 更新位置
          controlsPanel.style.left = left + 'px';
          controlsPanel.style.top = top + 'px';
          controlsPanel.style.bottom = 'auto';

          e.preventDefault();
        });

        // 触摸移动
        document.addEventListener('touchmove', function(e) {
          if (!isDragging) return;

          const touch = e.touches[0];
          let left = touch.clientX - dragOffsetX;
          let top = touch.clientY - dragOffsetY;

          // 边界检查
          const windowWidth = window.innerWidth;
          const windowHeight = window.innerHeight;

          // 确保不超出屏幕
          left = Math.max(0, Math.min(left, windowWidth - 70));
          top = Math.max(0, Math.min(top, windowHeight - 70));

          // 更新位置
          controlsPanel.style.left = left + 'px';
          controlsPanel.style.top = top + 'px';
          controlsPanel.style.bottom = 'auto';

          e.preventDefault();
        });

        // 结束拖动
        document.addEventListener('mouseup', function(e) {
          if (isDragging) {
            isDragging = false;
            controlHandle.style.cursor = 'grab';
            savePosition();
          }
        });

        // 触摸结束
        document.addEventListener('touchend', function(e) {
          if (isDragging) {
            console.log('触摸结束');
            isDragging = false;
            savePosition();

            // 计算移动距离
            const touch = e.changedTouches[0];
            const moveDistance = Math.sqrt(
                    Math.pow(touch.clientX - dragStartX, 2) +
                    Math.pow(touch.clientY - dragStartY, 2)
            );

            // 如果移动距离小于阈值，视为点击而不是拖动
            if (moveDistance < dragThreshold) {
              console.log('触摸点击打开菜单');
              toggleMenu();
            }
          }
        });
      }

      // 关闭按钮点击
      if (closeBtn) {
        closeBtn.addEventListener('click', function() {
          gameMenu.classList.remove('visible');
        });
      } else {
        console.error('找不到关闭按钮元素');
      }

      // 设置选项卡切换
      function setupTabSwitching() {
        tabBtns.forEach(btn => {
          btn.addEventListener('click', function() {
            const tabId = this.getAttribute('data-tab');

            // 更新活跃选项卡按钮
            tabBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            // 更新活跃面板
            tabPanes.forEach(pane => pane.classList.remove('active'));
            document.getElementById(tabId + '-tab').classList.add('active');
          });
        });
      }

      // 速度按钮点击事件
      speedBtns.forEach(btn => {
        // 初始化速度按钮状态
        const currentSpeed = parseInt(localStorage.getItem('gameSpeed') || '1');
        if(currentSpeed === parseInt(btn.getAttribute('data-speed'))) {
          btn.classList.add('active');
        }
        btn.addEventListener('click', function() {
          const speed = parseInt(this.getAttribute('data-speed'));
          localStorage.setItem('gameSpeed', speed);
          console.log('设置速度:', speed);

          // 执行游戏中的倍速功能
          try {
            if (typeof window.TF_LayerBattleUI.onClickSpeedUp === 'function') {
              // 使用原始的onClickSpeedUp函数，但绑定正确的this上下文
              window.TF_LayerBattleUI.onClickSpeedUp(true);
              speedBtns.forEach(b => b.classList.remove('active'));
              const btn = document.querySelector(`button[data-speed="${speed}"]`);
              if (btn) btn.classList.add('active');
            } else {
              alert('设置游戏速度失败');
            }
          } catch (error) {
            alert('设置游戏速度失败' + error);
            // console.error('设置游戏速度失败:', error);
          }
        });
      });

      // 开启vconsole调试按钮
      const debugConsoleBtn = document.getElementById('devbug-console');
      let vConsole = null;
      if (debugConsoleBtn) {
        // 检查本地存储中的状态
        const isVConsoleEnabled = localStorage.getItem('vconsole_enabled') === 'true';
        if (isVConsoleEnabled) {
          vConsole = new VConsole();
          debugConsoleBtn.textContent = '关闭控制台';
          debugConsoleBtn.style.background = 'linear-gradient(to bottom, #ff6b6b, #ff4757)';
        }

        debugConsoleBtn.addEventListener('click', function() {
          if (!vConsole) {
            // 创建并显示控制台
            vConsole = new VConsole();
            this.textContent = '关闭控制台';
            this.style.background = 'linear-gradient(to bottom, #ff6b6b, #ff4757)';
            localStorage.setItem('vconsole_enabled', 'true');
          } else {
            // 销毁控制台
            vConsole.destroy();
            vConsole = null;
            this.textContent = '显示控制台';
            this.style.background = 'linear-gradient(to bottom, #5a8bba, #4b79a1)';
            localStorage.setItem('vconsole_enabled', 'false');
          }
        });
      }

      const debugClearBtn = document.getElementById('debug-clear');
      if (debugClearBtn) {
        debugClearBtn.addEventListener('click', async function() {
          try {
            // 清除 localStorage
            localStorage.clear();

            // 清除 sessionStorage
            sessionStorage.clear();

            // 清除 IndexedDB
            const databases = await window.indexedDB.databases();
            databases.forEach(db => {
              if (db.name) {
                window.indexedDB.deleteDatabase(db.name);
              }
            });

            // 清除应用缓存
            if ('caches' in window) {
              const cacheKeys = await caches.keys();
              await Promise.all(cacheKeys.map(key => caches.delete(key)));
            }

            // 清除游戏控制面板位置
            localStorage.removeItem('gameControlsPosition');

            // 提示用户
            alert('缓存清除成功！');

            // 刷新页面
            window.location.reload();
          } catch (error) {
            console.error('清除缓存失败:', error);
            alert('清除缓存失败：' + error.message);
          }
        });
      }

      const debugReloadBtn = document.getElementById('debug-reload');
      if (debugReloadBtn) {
        debugReloadBtn.addEventListener('click', function() {
          window.location.reload();
        });
      }

      const debugLangBtn = document.getElementById('debug-lang');
      if (debugLangBtn) {
        debugLangBtn.addEventListener('click', function() {
          showLanguageModal();
        });
      }

      // 首充礼包按钮
      const debugFirstPayBtn = document.getElementById('debug-first-pay');
      if (debugFirstPayBtn) {
        debugFirstPayBtn.addEventListener('click', function() {
          showFirstPayGift();
        });
      }

      // 限时礼包按钮
      const debugLimitedGiftBtn = document.getElementById('debug-limited-gift');
      if (debugLimitedGiftBtn) {
        debugLimitedGiftBtn.addEventListener('click', function() {
          // 你可以修改这里的参数来测试不同类型的限时礼包
          // 参数1: 类型 ('skill', 'shop', 'pet')
          // 参数2: 等级 (1, 2, 3)
          showLimitedGift('skill', 1);
        });
      }

      // 能力提升礼包按钮
      const debugAbilityGiftBtn = document.getElementById('debug-ability-gift');
      if (debugAbilityGiftBtn) {
        debugAbilityGiftBtn.addEventListener('click', function() {
          // 默认显示类型1，您可以根据需要传入不同的类型
          showAbilityGift(3);
        });
      }

      // 体力补充礼包按钮
      const debugEnergyGiftBtn = document.getElementById('debug-energy-gift');
      if (debugEnergyGiftBtn) {
        debugEnergyGiftBtn.addEventListener('click', function() {
          showPowerGift();
        });
      }

      // 初始化控制面板
      function initGameControls() {
        // 初始速度按钮设置
        const activeSpeedBtn = document.querySelector(`button[data-speed="${currentSpeed}"]`);
        if (activeSpeedBtn) {
          activeSpeedBtn.classList.add('active');
        }

        // 变量保存定时器和状态
        let clickInterval = null;
        let clickCount = 0;

        // 模拟点击canvas函数
        function simulateClick(x, y) {
          const canvas = document.getElementById('GameCanvas');
          if (!canvas) {
            console.error('找不到Canvas元素');
            return;
          }

          // 获取canvas相对于视口的位置
          const rect = canvas.getBoundingClientRect();
          const clientX = rect.left + x;
          const clientY = rect.top + y;

          // 检测是移动设备还是桌面设备
          const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

          if (isMobile) {
            // 创建触摸事件
            const touchObj = new Touch({
              identifier: Date.now(),
              target: canvas,
              clientX: clientX,
              clientY: clientY,
              pageX: clientX,
              pageY: clientY,
              radiusX: 2.5,
              radiusY: 2.5,
              rotationAngle: 0,
              force: 1
            });

            // 触摸开始
            const touchStartEvent = new TouchEvent('touchstart', {
              bubbles: true,
              cancelable: true,
              view: window,
              touches: [touchObj],
              targetTouches: [touchObj],
              changedTouches: [touchObj]
            });
            canvas.dispatchEvent(touchStartEvent);

            // 触摸结束
            setTimeout(() => {
              const touchEndEvent = new TouchEvent('touchend', {
                bubbles: true,
                cancelable: true,
                view: window,
                touches: [],
                targetTouches: [],
                changedTouches: [touchObj]
              });
              canvas.dispatchEvent(touchEndEvent);

              clickCount++;
              console.log(`已点击 ${clickCount} 次，位置: (${x}, ${y})`);
            }, 50);
          } else {
            // 鼠标按下
            const mouseDownEvent = new MouseEvent('mousedown', {
              bubbles: true,
              cancelable: true,
              view: window,
              clientX: clientX,
              clientY: clientY,
              screenX: clientX,
              screenY: clientY,
              button: 0,
              buttons: 1
            });
            canvas.dispatchEvent(mouseDownEvent);

            // 鼠标释放和点击
            setTimeout(() => {
              const mouseUpEvent = new MouseEvent('mouseup', {
                bubbles: true,
                cancelable: true,
                view: window,
                clientX: clientX,
                clientY: clientY,
                screenX: clientX,
                screenY: clientY,
                button: 0,
                buttons: 0
              });
              canvas.dispatchEvent(mouseUpEvent);

              const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window,
                clientX: clientX,
                clientY: clientY,
                screenX: clientX,
                screenY: clientY,
                button: 0,
                buttons: 0
              });
              canvas.dispatchEvent(clickEvent);

              clickCount++;
              console.log(`已点击 ${clickCount} 次，位置: (${x}, ${y})`);
            }, 50);
          }
        }

        // 开始自动点击
        function startAutoClick() {
          if (clickInterval) return;
          const x = window.innerWidth / 2;
          const y = window.innerHeight - 100;
          const interval = 1000;
          clickCount = 0;
          // 立即执行一次点击
          simulateClick(x, y);
          // 设置定时器
          clickInterval = setInterval(() => {
            simulateClick(x, y);
          }, interval);
          console.log(`自动点击已启动，位置(${x}, ${y})，间隔${interval}ms`);
        }

        // 停止自动点击
        function stopAutoClick() {
          if (!clickInterval) return;
          window.isAutoBattleEnabled = false;
          clearInterval(clickInterval);
          clickInterval = null;

          const autoBattleBtn = document.getElementById('auto-battle');
          autoBattleBtn.classList.remove('active');
          autoBattleBtn.innerHTML = '开启自动战斗';
          console.log(`自动点击已停止，共点击${clickCount}次`);
        }

        window.startAutoClick = startAutoClick;
        window.stopAutoClick = stopAutoClick;
      }

      // 保存面板位置
      function savePosition() {
        const rect = controlsPanel.getBoundingClientRect();
        localStorage.setItem('gameControlsPosition', JSON.stringify({
          left: rect.left,
          top: rect.top
        }));
      }

      // 广告初始化
      var advsParams = {
        id: 'n66cd31be71e14',
        status: 2,
        playing: false
      }
      window.advsParams = advsParams
      function advInit() {
        console.log('initadv')
        window.OG_H5_GAME_SDK?.config({})
        window.OG_H5_GAME_SDK?.loadAds({id: advsParams.id})
        console.log('advsParams',advsParams, window.OG_H5_GAME_SDK)
        window.ug_loadAds = ({status}) => {
          console.log(1212121212, 'loadads')
          if (status === 1) {
            advsParams.status = 1
          }
        }
      }
      advInit()

      // 跳过战斗功能
      const skipBattleBtn = document.getElementById('skip-battle');
      console.log(skipBattleBtn, 'sdsd')
      if (skipBattleBtn) {
        console.log(12121)
        skipBattleBtn.addEventListener('click', function() {
          try {

            // 1. 首先尝试访问TF_LayerBattleUI实例
            const battleUI = window.TF_LayerBattleUI;
            if (!battleUI) {
              console.error('找不到TF_LayerBattleUI对象');
              return false;
            }
            let level = battleUI._curLevel;
            let percent = 100;
            let killNumber = 500;

            console.log(`尝试完成关卡 ${level}，评分 ${percent}%，击杀数 ${killNumber}`);
            console.log('confirm Level!!!', battleUI._curLevel)
            console.log('找到TF_LayerBattleUI对象');

            // 2. 直接从实例中获取需要的对象
            try {
              // 获取Battle对象
              const battle = battleUI.TF_Battle;
              if (battle) {
                console.log('获取到TF_Battle对象');

                // 保存原始的openLayerBattleWin方法
                const originalOpenLayerBattleWin = battle.openLayerBattleWin;

                // 设置查找次数和成功标志
                let findAttempts = 0;
                const maxFindAttempts = 10;
                let requestSuccessful = false;

                // 创建一个函数来查找胜利界面并调用request方法
                const findWinUIAndRequest = function() {
                  // 检查是否已经成功或者超过最大尝试次数
                  if (requestSuccessful || findAttempts >= maxFindAttempts) {
                    console.log('停止查找：' + (requestSuccessful ? '已成功发送请求' : '已达到最大尝试次数'));

                    // 恢复原始方法
                    if (originalOpenLayerBattleWin) {
                      battle.openLayerBattleWin = originalOpenLayerBattleWin;
                      console.log('已恢复原始openLayerBattleWin方法');
                    }

                    return;
                  }

                  findAttempts++;
                  console.log(`开始查找胜利界面并调用request方法...（第${findAttempts}次尝试）`);

                  if (!cc || !cc.director || !cc.director.getScene()) {
                    console.log('场景未加载，延迟查找');
                    setTimeout(findWinUIAndRequest, 500);
                    return;
                  }

                  const scene = cc.director.getScene();
                  let winUI = null;

                  // 递归查找胜利界面
                  function findWinUI(node) {
                    if (!node) return null;

                    // 检查组件
                    const components = node._components || [];
                    for (let i = 0; i < components.length; i++) {
                      const comp = components[i];
                      if (comp &&
                              comp.constructor &&
                              typeof comp.request === 'function' &&
                              (comp.constructor.name.includes('TF_LayerBattleWinUI') ||
                                      comp.constructor.name.includes('BattleWin'))) {
                        return comp;
                      }
                    }

                    // 检查子节点
                    const children = node.children || [];
                    for (let i = 0; i < children.length; i++) {
                      const result = findWinUI(children[i]);
                      if (result) return result;
                    }

                    return null;
                  }

                  winUI = findWinUI(scene);

                  if (winUI) {
                    console.log('找到胜利界面实例');

                    // 修改TF_SystemData中的killNum
                    if (winUI.TF_SystemData && winUI.TF_SystemData.TF_SubSystemMonsterData) {
                      console.log('修改击杀数为:', killNumber);
                      winUI.TF_SystemData.TF_SubSystemMonsterData.killNum = killNumber;
                    }

                    if (typeof winUI.request === 'function') {
                      console.log('调用request方法');
                      winUI.request(level, percent);
                      console.log('成功调用request方法');

                      // 设置成功标志
                      requestSuccessful = true;

                      // 恢复原始方法
                      if (originalOpenLayerBattleWin) {
                        battle.openLayerBattleWin = originalOpenLayerBattleWin;
                        console.log('已恢复原始openLayerBattleWin方法');
                      }

                      return true;
                    } else {
                      console.log('找到的实例没有request方法');
                    }
                  } else {
                    console.log('未找到胜利界面实例，稍后再试');

                    // 只有在未成功且未达到最大尝试次数时继续查找
                    if (!requestSuccessful && findAttempts < maxFindAttempts) {
                      setTimeout(findWinUIAndRequest, 300);
                    } else {
                      console.log('停止查找：已达到最大尝试次数');

                      // 恢复原始方法
                      if (originalOpenLayerBattleWin) {
                        battle.openLayerBattleWin = originalOpenLayerBattleWin;
                        console.log('已恢复原始openLayerBattleWin方法');
                      }
                    }
                  }
                };

                // 重写openLayerBattleWin方法
                battle.openLayerBattleWin = function(params) {
                  console.log('调用被修改的openLayerBattleWin方法');

                  // 调用原始方法
                  originalOpenLayerBattleWin.call(this, params);

                  // 延迟执行，确保界面已创建
                  setTimeout(findWinUIAndRequest, 500);
                };

                // 打开胜利UI界面
                if (typeof battle.openLayerBattleWin === 'function') {
                  console.log('准备调用openLayerBattleWin方法');

                  const params = {
                    level: level,
                    percent: percent,
                    type: battle.StartConfig ? battle.StartConfig.battleType : 0
                  };

                  // 调用方法
                  battle.openLayerBattleWin(params);
                  console.log('成功调用openLayerBattleWin方法');
                  return true;
                }
              }
            } catch (e) {
              console.error('访问TF_Battle对象失败:', e);
            }

            console.log('无法找到或使用TF_Battle对象');
            return false;
          } catch (error) {
            console.error('初始化战斗胜利功能时发生错误:', error);
          }
        });
      }

      // 自动战斗功能
      const autoBattleBtn = document.getElementById('auto-battle');
      window.isAutoBattleEnabled = false;

      if (autoBattleBtn) {
        autoBattleBtn.addEventListener('click', function() {
          // 切换自动战斗状态
          window.isAutoBattleEnabled = !window.isAutoBattleEnabled;

          if (window.isAutoBattleEnabled) {
            // 开启自动战斗
            this.textContent = '关闭自动战斗';
            this.classList.add('active');
            console.log('自动战斗已启动');

            // 开始自动战斗逻辑
            window.startAutoClick()
          } else {
            // 关闭自动战斗
            this.textContent = '开启自动战斗';
            this.classList.remove('active');
            console.log('自动战斗已停止');

            // 停止自动战斗逻辑
            window.stopAutoClick()
          }
        });
      }

      // 后台发物品功能
      const itemSelect = document.getElementById('item-select');
      const itemQuantity = document.getElementById('item-quantity');
      const sendItemBtn = document.getElementById('send-item');

      sendItemBtn.addEventListener('click', async function() {
        const accountNum = localStorage.getItem('account_num');
        if (!accountNum) {
          alert('未找到账号信息');
          return;
        }

        const quantity = itemQuantity.value;
        if (!quantity || quantity < 1) {
          alert('请输入有效的数量');
          return;
        }

        const itemId = itemSelect.value;

        try {
          // TY:游戏配置 请求连接修改
          const response = await fetch('https://xian-test.uggamer.com//gm/api.php', {
            method: 'POST',
            mode: 'cors', // 明确指定CORS模式
            credentials: 'include', // 包含凭证
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
              usr: accountNum,
              bao: '0',
              num: quantity,
              item: itemId,
              gnxz: '2'
            })
          });

          const result = await response.text();
          if (result.includes('success') || result.includes('成功')) {
            alert('物品发送成功！请在信件中领取');
            itemQuantity.value = ''; // 清空输入框
          } else {
            alert('发送失败：' + result);
          }
        } catch (error) {
          console.error('发送请求失败:', error);
          alert('发送失败，请检查网络连接');
        }
      });

      // 初始化
      setupDragFunctionality();
      setupTabSwitching();
      initGameControls();
      initLanguageModal();
      initDeleteAccount()

      // 标记初始化完成
      console.log('游戏控制面板初始化完成');

      let currentLang = 'zh';
      const langBtn = document.querySelector('.lang-btn');
      function setLang(lang) {
        // 标题
        document.querySelector('.menu-title').textContent = langMap[lang]['游戏工具'];
        // 标签
        const tabBtns = document.querySelectorAll('.tab-btn');
        tabBtns[0].textContent = langMap[lang]['倍速'];
        tabBtns[1].textContent = langMap[lang]['外挂'];
        tabBtns[2].textContent = langMap[lang]['调试'];
        // 速度tab
        document.querySelector('#speed-tab .option-title').textContent = langMap[lang]['游戏倍速'];
        document.querySelector('#speed-tab .option-title + .option-buttons + .option-title').textContent = langMap[lang]['战斗控制'];
        document.getElementById('skip-battle').textContent = langMap[lang]['跳过战斗'];
        const autoBattleBtn = document.getElementById('auto-battle');
        if (autoBattleBtn.classList.contains('active')) {
          autoBattleBtn.textContent = lang === 'zh' ? '关闭自动战斗' : 'ปิดออโต้แบทเทิล';
        } else {
          autoBattleBtn.textContent = langMap[lang]['开启自动战斗'];
        }
        // tips
        const battleTips = document.querySelectorAll('#speed-tab .battle-buttons div');
        battleTips[0].textContent = langMap[lang]['tips:跳过战斗无法获取与正常战斗相同的物资奖励'];
        battleTips[1].textContent = langMap[lang]['tips:开启自动战斗后会触发自动点击事件，请开启后立刻进入战斗；若手动暂停退出战斗，需要手动关闭自动战斗，胜利或失败会自动关闭'];
        // 外挂tab
        document.querySelector('#auto-tab .option-title').textContent = langMap[lang]['获取物品'];
        document.getElementById('item-quantity').placeholder = langMap[lang]['输入数量'];
        document.getElementById('send-item').textContent = langMap[lang]['获取物品'];
        // 调试tab
        document.querySelector('#debug-tab .option-title').textContent = langMap[lang]['调试工具'];
        document.getElementById('devbug-console').textContent = langMap[lang]['显示控制台'];
        document.getElementById('debug-clear').textContent = langMap[lang]['清除缓存'];
        // 下拉框选项
        setItemOptions(lang);
        // 切换按钮文本
        langBtn.textContent = lang === 'zh' ? 'ไทย' : '中文';
      }
      langBtn.addEventListener('click', function() {
        currentLang = currentLang === 'zh' ? 'th' : 'zh';
        setLang(currentLang);
      });
      // 替换弹窗和alert相关文本
      const oldAlert = window.alert;
      window.alert = function(msg) {
        if (typeof msg === 'string') {
          if (currentLang === 'th') {
            for (const key in langMap.zh) {
              if (msg.indexOf(key) !== -1) {
                msg = msg.replace(key, langMap.th[key]);
              }
            }
          } else {
            for (const key in langMap.th) {
              if (msg.indexOf(langMap.th[key]) !== -1) {
                msg = msg.replace(langMap.th[key], key);
              }
            }
          }
        }
        oldAlert(msg);
      };
      // 初始化默认语言
      setLang(currentLang);
      function setItemOptions(lang) {
        const select = document.getElementById('item-select');
        for (const option of select.options) {
          if (lang === 'th') {
            if (itemOptionsMap[option.text]) option.text = itemOptionsMap[option.text];
          } else {
            // 反向查找
            for (const zh in itemOptionsMap) {
              if (option.text === itemOptionsMap[zh]) {
                option.text = zh;
                break;
              }
            }
          }
        }
      }



      // 多语言弹窗功能
      function showLanguageModal() {
        const modal = document.getElementById('language-modal');
        if (modal) {
          modal.classList.add('show');
          updateSelectedLanguage();
        }
      }

      function hideLanguageModal() {
        const modal = document.getElementById('language-modal');
        if (modal) {
          modal.classList.remove('show');
        }
      }

      function updateSelectedLanguage() {
        const options = document.querySelectorAll('.language-option');
        const currentLang = getCurrentLanguage();

        options.forEach(option => {
          option.classList.remove('selected');
          if (option.getAttribute('data-lang') === currentLang) {
            option.classList.add('selected');
          }
        });
      }

      function getCurrentLanguage() {
        // 根据当前网址判断当前语言
        const url = window.location.href;
        if (url.includes('zh')) return 'zh';
        if (url.includes('en')) return 'en';
        if (url.includes('th')) return 'th';
        return 'th'; // 默认泰文
      }

      function setLanguage(lang) {
        // 根据选择的语言更新界面
        switch(lang) {
          case 'zh':
            // 设置为繁体中文
            window.flutterObj.changeLanguage('3')
            break;
          case 'en':
            // 设置为英文 (暂时使用中文，可以后续扩展)
            window.flutterObj.changeLanguage('1')
            break;
          case 'th':
            // 设置为泰文
            window.flutterObj.changeLanguage('2')
            break;
          default:
            // 默认中文
            window.flutterObj.changeLanguage('2')
            break;
        }

        // 更新语言弹窗标题
        updateLanguageModalTitle(lang);
        updateDeleteAccountModalTitle(lang);
        hideLanguageModal();
      }

      function updateLanguageModalTitle(lang) {
        const modalTitle = document.querySelector('.language-modal-title');
        if (modalTitle) {
          modalTitle.textContent = langMap[lang]['切换语言'];
        }
      }

      // 初始化多语言弹窗事件
      function initLanguageModal() {
        const modal = document.getElementById('language-modal');
        const closeBtn = document.querySelector('.language-modal-close');
        const options = document.querySelectorAll('.language-option');

        // 关闭按钮事件
        if (closeBtn) {
          closeBtn.addEventListener('click', hideLanguageModal);
        }

        // 点击背景关闭弹窗
        if (modal) {
          modal.addEventListener('click', function(e) {
            if (e.target === modal) {
              hideLanguageModal();
            }
          });
        }

        // 语言选项点击事件
        options.forEach(option => {
          option.addEventListener('click', function() {
            const lang = this.getAttribute('data-lang');
            setLanguage(lang);
          });
        });

        // 初始化弹窗标题
        updateLanguageModalTitle(getCurrentLanguage());
      }
      window.showLanguage = showLanguageModal
      window.getCurrentLanguage = getCurrentLanguage
      console.log(showLanguageModal, 'showLanguageModal')
      window.hideLanguage = hideLanguageModal

      function initDeleteAccount() {
        const deleteAccountBtn = document.getElementById("delete-account-btn");
        const deleteAccount = document.getElementById("delete-account");
        const deleteAccountClose = document.querySelector(".delete-account-close");
        if (deleteAccountBtn) {
          deleteAccountBtn.addEventListener("click", function () {
            deleteAccountFunc()
          });
        }
        if (deleteAccountClose) {
          deleteAccountClose.addEventListener("click", function () {
            deleteAccount.classList.remove("show");
          });
        }

        updateDeleteAccountModalTitle(getCurrentLanguage());
      }

      function updateDeleteAccountModalTitle(lang) {
        const modalTitle = document.querySelector(".delete-account-title");
        if (modalTitle) {
          console.log('sdsdsds',langMap[lang]["删除账号"])
          modalTitle.textContent = langMap[lang]["删除账号"];
        }
        const deleteAccountBtn = document.getElementById("delete-account-btn");
        if (deleteAccountBtn) {
          console.log('sdsdsds',langMap[lang]["确认"])
          deleteAccountBtn.textContent = langMap[lang]["确认"];
        }
        const deleteAccountInput1 = document.getElementById("delete-account-input1");
        if (deleteAccountInput1) {
          console.log('sdsdsds',langMap[lang]["密码place1"])
          deleteAccountInput1.placeholder = langMap[lang]["密码place1"];
        }
        const deleteAccountInput2 = document.getElementById("delete-account-input2");
        if (deleteAccountInput2) {
          deleteAccountInput2.placeholder = langMap[lang]["密码place2"];
        }
      }

      function showDeleteAccount() {
        const deleteAccount = document.getElementById("delete-account");
        if (deleteAccount) {
          deleteAccount.classList.add("show");
        }
      }

      function deleteAccountFunc() {
        const deleteAccountInput1 = document.getElementById("delete-account-input1");
        const deleteAccountInput2 = document.getElementById("delete-account-input2");
        if (deleteAccountInput1.value === deleteAccountInput2.value) {
          const password = btoa(deleteAccountInput1.value.trim());
          // TY:游戏配置 请求连接修改
          fetch('https://logs.uggamer.com/api/deleteAccount', {
            method: 'POST',
            mode: 'cors', // 明确指定CORS模式
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              account: localStorage.getItem('account_num'),
              service: 'prod',
              password
            })
          }).then((res) => {
            return res.json()
          }).then((res) => {
            if (res.success) {
              window.flutterObj.exitLogin();
              window.location.reload();
            } else {
              alert(langMap[getCurrentLanguage()]["密码错误"]);
            }
          }).catch((err) => {
            console.log(err);
          })
        } else {
          alert(langMap[getCurrentLanguage()]["两次输入的密码不一致"]);
        }
      }

      window.showDeleteAccount = showDeleteAccount;

      // 首充礼包逻辑
      let currentGiftPage = 1; // 当前显示的礼包分页
      let unlockedGiftPage = 1; // 当前已解锁的分页
      let selectedItemId = null; // 当前选中的物品ID

      // 购买状态管理 - 假设从服务器获取的数据
      let purchasedGiftLevels = {
        1: false, // 一级礼包是否已购买
        2: false, // 二级礼包是否已购买
        3: false  // 三级礼包是否已购买
      };

      // 模拟从服务器获取购买状态的函数
      function fetchPurchaseStatus() {
        // 这里应该是实际的API调用
        // 现在先用假数据进行测试
        // 你可以修改这些值来测试不同的购买状态
        return {
          1: false, // 假设一级礼包未购买
          2: false, // 假设二级礼包未购买
          3: false  // 假设三级礼包未购买
        };
      }

      // 更新购买状态
      function updatePurchaseStatus(newStatus) {
        purchasedGiftLevels = { ...purchasedGiftLevels, ...newStatus };
        updateGiftDisplay(); // 更新显示状态
      }

      // 检查是否可以购买指定等级的礼包
      function canPurchaseLevel(level) {
        if (level === 1) {
          return true; // 一级礼包总是可以购买
        }
        // 二级及以上需要前一级已购买
        return purchasedGiftLevels[level - 1] === true;
      }

      // 礼包数据配置
      const giftPackData = {
        1: {
          image: '/gift-pic/首充礼包/png/物品缩放/lv1.png',
          text: '紫色珍珠三选一',
          price: '$0.49',
          product_id: 'com.ol.fishstorm.survival.io.first.charge1',
          items: [
            [{
              id: 1003036,
              num: 1
            }],
            [{
              id: 1003037,
              num: 1
            }],
            [{
              id: 1003038,
              num: 1
            }]
          ]
        },
        2: {
          image: '/gift-pic/首充礼包/png/物品缩放/lv2.png',
          text: '金色珍珠三选一',
          price: '$1.99',
          product_id: 'com.ol.fishstorm.survival.io.first.charge2',
          items: [
            [{
              id: 3004001,
              num: 1
            },
            {
              id: 2004010,
              num: 1
            }],
            [
              {
                id: 3004006,
                num: 1
              },
              {
                id: 2004008,
                num: 1
              }
            ],
            [
              {
                id: 3004007,
                num: 1
              },
              {
                id: 2004009,
                num: 1
              }
            ]
          ]
        },
        3: {
          image: '/gift-pic/首充礼包/png/物品缩放/lv3.png',
          text: '红色珍珠三选一',
          price: '$4.99',
          product_id: 'com.ol.fishstorm.survival.io.first.charge3',
          items: [
            [{
              id: 4004001,
              num: 1
            },
            {
              id: 5004001,
              num: 1
            },
            {
              id: 6004010,
              num: 1
            }],
            [
              {
                id: 4004006,
                num: 1
              },
              {
                id: 5004006,
                num: 1
              },
              {
                id: 6004008,
                num: 1
              }
            ],
            [
              {
                id: 4004007,
                num: 1
              },
              {
                id: 5004007,
                num: 1
              },
              {
                id: 6004009,
                num: 1
              }
            ]
          ]
        }
      };

      // 初始化首充礼包
      function initFirstPayGift() {
        const giftOptions = document.querySelectorAll('.first-pay-gift-option');
        const giftItems = document.querySelectorAll('.first-pay-gift-item');
        const giftText = document.querySelector('.first-pay-text');
        const giftBtn = document.querySelector('.first-pay-gift-btn');
        const closeBtn = document.querySelector('.first-pay-gift-close');

        // 关闭按钮事件
        if (closeBtn) {
          closeBtn.addEventListener('click', function() {
            document.getElementById('first-pay-gift').style.display = 'none';
            selectedItemId = null; // 重置选中状态
          });
        }

        // 分页点击事件
        giftOptions.forEach((option, index) => {
          const level = index + 1;
          option.addEventListener('click', function() {
            switchGiftPage(level);
          });
        });

        // 物品点击事件
        giftItems.forEach((item) => {
          item.addEventListener('click', function() {
            selectGiftItem(this);
          });
        });

        // 支付按钮点击事件
        if (giftBtn) {
          giftBtn.addEventListener('click', function() {
            handleFirstPayGiftPurchase();
          });
        }

        // 初始化显示
        updateGiftDisplay();
        updatePayButtonState();
      }

      // 切换礼包分页
      function switchGiftPage(level) {
        currentGiftPage = level;
        selectedItemId = null; // 切换分页时重置选中状态
        updateGiftDisplay();
        updatePayButtonState();
      }

      // 选中物品
      function selectGiftItem(itemElement) {
        const itemId = itemElement.getAttribute('data-item-id');

        // 移除所有选中状态
        document.querySelectorAll('.first-pay-gift-item').forEach(item => {
          item.classList.remove('selected');
        });

        // 添加选中状态
        itemElement.classList.add('selected');
        selectedItemId = itemId;

        // 更新支付按钮状态
        updatePayButtonState();

        console.log('选中物品ID:', itemId);
      }

      // 更新支付按钮状态
      function updatePayButtonState() {
        const giftBtn = document.querySelector('.first-pay-gift-btn');
        if (giftBtn) {
          if (selectedItemId) {
            giftBtn.classList.remove('disabled');
            giftBtn.style.pointerEvents = 'auto';
          } else {
            giftBtn.classList.add('disabled');
            giftBtn.style.pointerEvents = 'none';
          }
        }
      }

      // 更新礼包显示
      function updateGiftDisplay() {
        const giftOptions = document.querySelectorAll('.first-pay-gift-option');
        const giftItems = document.querySelectorAll('.first-pay-gift-item img');
        const giftItemContainers = document.querySelectorAll('.first-pay-gift-item');
        const giftText = document.querySelector('.first-pay-text');
        const giftBtn = document.querySelector('.first-pay-gift-btn');

        // 更新分页状态
        giftOptions.forEach((option, index) => {
          const level = index + 1;
          option.classList.remove('active', 'lock', 'purchased');

          if (level === currentGiftPage) {
            option.classList.add('active');
          }

          if (level > unlockedGiftPage) {
            option.classList.add('lock');
          }

          // 添加已购买状态
          if (purchasedGiftLevels[level]) {
            option.classList.add('purchased');
          }
        });

        // 清除所有物品的选中状态
        giftItemContainers.forEach(item => {
          item.classList.remove('selected');
        });

        // 更新礼包内容
        const currentData = giftPackData[currentGiftPage];
        if (currentData) {
          giftItems.forEach(img => {
            img.src = currentData.image;
          });

          if (giftText) {
            giftText.textContent = currentData.text;
          }

          if (giftBtn) {
            const canPurchase = canPurchaseLevel(currentGiftPage);
            const alreadyPurchased = purchasedGiftLevels[currentGiftPage];

            if (alreadyPurchased) {
              giftBtn.textContent = '已购买';
              giftBtn.classList.add('purchased');
              giftBtn.classList.remove('lock', 'disabled');
              giftBtn.style.pointerEvents = 'none';
            } else if (!canPurchase) {
              giftBtn.textContent = '需先购买前置礼包';
              giftBtn.classList.add('lock');
              giftBtn.classList.remove('purchased', 'disabled');
              giftBtn.style.pointerEvents = 'none';
            } else if (currentGiftPage > unlockedGiftPage) {
              giftBtn.textContent = currentData.price;
              giftBtn.classList.add('lock');
              giftBtn.classList.remove('purchased', 'disabled');
              giftBtn.style.pointerEvents = 'none';
            } else {
              giftBtn.textContent = currentData.price;
              giftBtn.classList.remove('lock', 'purchased');
              // 支付按钮状态由选中状态控制，这里不直接设置
            }
          }
        }
      }

      // 解锁下一个礼包分页
      function unlockNextGiftPage() {
        if (unlockedGiftPage < 3) {
          unlockedGiftPage++;
          updateGiftDisplay();
        }
      }

      // 显示首充礼包
      function showFirstPayGift() {
        selectedItemId = null; // 重置选中状态

        // 获取最新的购买状态
        purchasedGiftLevels = fetchPurchaseStatus();

        const giftModal = document.getElementById('first-pay-gift');
        if (giftModal) {
          giftModal.style.display = 'flex';
          initFirstPayGift();
        }
      }

      // 处理首充礼包购买
      function handleFirstPayGiftPurchase() {
        // 检查是否已购买
        if (purchasedGiftLevels[currentGiftPage]) {
          alert('该礼包已购买');
          return;
        }

        // 检查是否可以购买（前置条件）
        if (!canPurchaseLevel(currentGiftPage)) {
          alert('请先购买前置礼包');
          return;
        }

        // 检查是否已解锁
        if (currentGiftPage > unlockedGiftPage) {
          alert('该礼包尚未解锁');
          return;
        }

        if (!selectedItemId) {
          alert('请先选择一个物品');
          return;
        }

        const currentGiftData = giftPackData[currentGiftPage];
        if (!currentGiftData) {
          console.error('找不到当前礼包数据');
          return;
        }

        // 获取选中物品的索引（selectedItemId是字符串"1","2","3"，需要转换为数组索引0,1,2）
        const itemIndex = parseInt(selectedItemId) - 1;
        const selectedItems = currentGiftData.items[itemIndex];

        if (!selectedItems) {
          console.error('找不到选中物品的数据');
          return;
        }

        // 获取产品ID和物品数据
        const productId = currentGiftData.product_id;
        const itemsData = selectedItems;

        // 输出购买信息
        console.log('首充礼包购买信息:', {
          giftLevel: currentGiftPage,
          selectedItemId: selectedItemId,
          productId: productId,
          itemsData: itemsData,
          price: currentGiftData.price
        });
        window.payObj.payGift(productId, itemsData)
        // 这里可以添加实际的购买逻辑
        alert(`购买信息：\n礼包等级: Lv${currentGiftPage}\n选中物品: ${selectedItemId}\n产品ID: ${productId}\n物品数据: ${JSON.stringify(itemsData)}\n价格: ${currentGiftData.price}`);

        // 模拟购买成功 - 实际项目中应该在支付成功回调中调用
        simulatePurchaseSuccess(currentGiftPage);
      }

      // 模拟购买成功的函数 - 实际项目中应该在支付成功回调中调用
      function simulatePurchaseSuccess(level) {
        // 更新购买状态
        purchasedGiftLevels[level] = true;

        // 更新显示
        updateGiftDisplay();

        console.log(`礼包 Lv${level} 购买成功`);

        // 如果购买的是当前最高级别，可以解锁下一级
        if (level === unlockedGiftPage && level < 3) {
          unlockNextGiftPage();
        }
      }

      // 体力补充礼包相关函数
      function showPowerGift() {
        const powerGiftModal = document.getElementById('power-gift');
        if (powerGiftModal) {
          powerGiftModal.style.display = 'flex';
          initPowerGift();
        }
      }

      function closePowerGift() {
        const powerGiftModal = document.getElementById('power-gift');
        if (powerGiftModal) {
          powerGiftModal.style.display = 'none';
        }
      }

      function initPowerGift() {
        const closeBtn = document.querySelector('.power-gift-close');
        const purchaseBtn = document.querySelector('.power-gift-btn');

        // 关闭按钮事件
        if (closeBtn) {
          closeBtn.addEventListener('click', function() {
            closePowerGift();
          });
        }

        // 购买按钮事件
        if (purchaseBtn) {
          purchaseBtn.addEventListener('click', function() {
            handlePowerGiftPurchase();
          });
        }
      }

      function handlePowerGiftPurchase() {
        // 空函数，待后续实现
        console.log('体力补充礼包购买按钮被点击');
      }

      // 能力提升礼包相关函数
      // 礼包类型配置
      const abilityGiftTypes = {
        1: { price: '$1.99', image: '/gift-pic/能力提升/png/泰弹窗.png' },
        2: { price: '$1.99', image: '/gift-pic/能力提升/png/泰弹窗2.png' },
        3: { price: '$4.99', image: '/gift-pic/能力提升/png/泰弹窗3.png' },
        4: { price: '$4.99', image: '/gift-pic/能力提升/png/泰弹窗4.png' },
        5: { price: '$9.99', image: '/gift-pic/能力提升/png/泰弹窗5.png' }
      };

      let currentAbilityGiftType = 1; // 当前礼包类型

      function showAbilityGift(giftType = 1) {
        currentAbilityGiftType = giftType;
        const abilityGiftModal = document.getElementById('ability-gift');
        if (abilityGiftModal) {
          updateAbilityGiftDisplay(giftType);
          abilityGiftModal.style.display = 'flex';
          initAbilityGift();
        }
      }

      function updateAbilityGiftDisplay(giftType) {
        const giftData = abilityGiftTypes[giftType];
        if (!giftData) return;

        // 更新背景图片
        const giftContent = document.querySelector('.ability-gift-content');
        if (giftContent) {
          giftContent.style.backgroundImage = `url("${giftData.image}")`;
        }

        // 更新价格
        const priceBtn = document.querySelector('.ability-gift-btn');
        if (priceBtn) {
          priceBtn.textContent = giftData.price;
        }
      }

      function closeAbilityGift() {
        const abilityGiftModal = document.getElementById('ability-gift');
        if (abilityGiftModal) {
          abilityGiftModal.style.display = 'none';
        }
      }

      function initAbilityGift() {
        const closeBtn = document.querySelector('.ability-gift-close');
        const purchaseBtn = document.querySelector('.ability-gift-btn');

        // 移除之前的事件监听器，避免重复绑定
        if (closeBtn) {
          closeBtn.replaceWith(closeBtn.cloneNode(true));
        }
        if (purchaseBtn) {
          purchaseBtn.replaceWith(purchaseBtn.cloneNode(true));
        }

        // 重新获取元素并绑定事件
        const newCloseBtn = document.querySelector('.ability-gift-close');
        const newPurchaseBtn = document.querySelector('.ability-gift-btn');

        // 关闭按钮事件
        if (newCloseBtn) {
          newCloseBtn.addEventListener('click', function() {
            closeAbilityGift();
          });
        }

        // 购买按钮事件
        if (newPurchaseBtn) {
          newPurchaseBtn.addEventListener('click', function() {
            handleAbilityGiftPurchase();
          });
        }
      }

      function handleAbilityGiftPurchase() {
        // 空函数，待后续实现
        console.log('能力提升礼包购买按钮被点击，类型:', currentAbilityGiftType);
      }

      // 限时礼包相关函数
      // 统一的限时礼包显示函数
      function showLimitedGift(type, level = 1) {
        switch(type) {
          case 'skill':
            showLimitedGiftSkill(level);
            break;
          case 'shop':
            showLimitedGiftShop(level);
            break;
          case 'pet':
            showLimitedGiftPet(level);
            break;
          default:
            console.error('未知的限时礼包类型:', type);
            alert('未知的限时礼包类型: ' + type);
        }
      }

      // 技能类型限时礼包
      const skillGiftTypes = {
        1: { price: '$1.99', image: '/gift-pic/限时礼包/泰/1.png' },
        2: { price: '$4.99', image: '/gift-pic/限时礼包/泰/2.png' },
        3: { price: '$9.99', image: '/gift-pic/限时礼包/泰/3.png' }
      };

      let currentSkillGiftLevel = 1;

      function showLimitedGiftSkill(level = 1) {
        currentSkillGiftLevel = level;
        const skillGiftModal = document.getElementById('limited-gift-skill');
        if (skillGiftModal) {
          updateSkillGiftDisplay(level);
          skillGiftModal.style.display = 'flex';
          initSkillGift();
        }
      }

      function updateSkillGiftDisplay(level) {
        const giftData = skillGiftTypes[level];
        if (!giftData) return;

        // 更新背景图片
        const giftContent = document.querySelector('.limited-gift-skill-content');
        if (giftContent) {
          giftContent.style.backgroundImage = `url("${giftData.image}")`;
        }

        // 更新价格
        const priceBtn = document.querySelector('.limited-gift-skill-btn');
        if (priceBtn) {
          priceBtn.textContent = giftData.price;
        }

        // 更新标签状态
        const tabs = document.querySelectorAll('.limited-gift-skill-tab');
        tabs.forEach((tab, index) => {
          tab.classList.remove('active');
          if (index + 1 === level) {
            tab.classList.add('active');
          }
        });
      }

      function closeLimitedGiftSkill() {
        const skillGiftModal = document.getElementById('limited-gift-skill');
        if (skillGiftModal) {
          skillGiftModal.style.display = 'none';
        }
      }

      function initSkillGift() {
        const closeBtn = document.querySelector('.limited-gift-skill-close');
        const purchaseBtn = document.querySelector('.limited-gift-skill-btn');
        const tabs = document.querySelectorAll('.limited-gift-skill-tab');

        // 关闭按钮事件
        if (closeBtn) {
          closeBtn.addEventListener('click', function() {
            closeLimitedGiftSkill();
          });
        }

        // 购买按钮事件
        if (purchaseBtn) {
          purchaseBtn.addEventListener('click', function() {
            handleSkillGiftPurchase();
          });
        }

        // 标签切换事件
        tabs.forEach(tab => {
          tab.addEventListener('click', function() {
            const level = parseInt(this.getAttribute('data-level'));
            updateSkillGiftDisplay(level);
            currentSkillGiftLevel = level;
          });
        });
      }

      function handleSkillGiftPurchase() {
        console.log('技能类型限时礼包购买按钮被点击，等级:', currentSkillGiftLevel);
        const giftData = skillGiftTypes[currentSkillGiftLevel];
        alert(`购买技能类型限时礼包 Lv.${currentSkillGiftLevel}\n价格: ${giftData.price}`);
      }

      // 将函数暴露到全局
      window.showFirstPayGift = showFirstPayGift;
      window.unlockNextGiftPage = unlockNextGiftPage;
      window.handleFirstPayGiftPurchase = handleFirstPayGiftPurchase;
      window.updatePurchaseStatus = updatePurchaseStatus;
      window.simulatePurchaseSuccess = simulatePurchaseSuccess;
      window.fetchPurchaseStatus = fetchPurchaseStatus;
      window.showPowerGift = showPowerGift;
      window.closePowerGift = closePowerGift;
      window.handlePowerGiftPurchase = handlePowerGiftPurchase;
      window.showAbilityGift = showAbilityGift;
      window.closeAbilityGift = closeAbilityGift;
      window.handleAbilityGiftPurchase = handleAbilityGiftPurchase;
      window.showLimitedGiftSkill = showLimitedGiftSkill;
      window.closeLimitedGiftSkill = closeLimitedGiftSkill;
      window.handleSkillGiftPurchase = handleSkillGiftPurchase;

      // 商城类型限时礼包
      const shopGiftTypes = {
        1: { price: '$2.99', image: '/gift-pic/限时礼包/泰/4.png' },
        2: { price: '$5.99', image: '/gift-pic/限时礼包/泰/5.png' },
        3: { price: '$12.99', image: '/gift-pic/限时礼包/泰/6.png' }
      };

      let currentShopGiftLevel = 1;

      function showLimitedGiftShop(level = 1) {
        currentShopGiftLevel = level;
        const shopGiftModal = document.getElementById('limited-gift-shop');
        if (shopGiftModal) {
          updateShopGiftDisplay(level);
          shopGiftModal.style.display = 'flex';
          initShopGift();
        }
      }

      function updateShopGiftDisplay(level) {
        const giftData = shopGiftTypes[level];
        if (!giftData) return;

        // 更新背景图片
        const giftContent = document.querySelector('.limited-gift-shop-content');
        if (giftContent) {
          giftContent.style.backgroundImage = `url("${giftData.image}")`;
        }

        // 更新价格
        const priceBtn = document.querySelector('.limited-gift-shop-btn');
        if (priceBtn) {
          priceBtn.textContent = giftData.price;
        }

        // 更新标签状态
        const tabs = document.querySelectorAll('.limited-gift-shop-tab');
        tabs.forEach((tab, index) => {
          tab.classList.remove('active');
          if (index + 1 === level) {
            tab.classList.add('active');
          }
        });
      }

      function closeLimitedGiftShop() {
        const shopGiftModal = document.getElementById('limited-gift-shop');
        if (shopGiftModal) {
          shopGiftModal.style.display = 'none';
        }
      }

      function initShopGift() {
        const closeBtn = document.querySelector('.limited-gift-shop-close');
        const purchaseBtn = document.querySelector('.limited-gift-shop-btn');
        const tabs = document.querySelectorAll('.limited-gift-shop-tab');

        // 关闭按钮事件
        if (closeBtn) {
          closeBtn.addEventListener('click', function() {
            closeLimitedGiftShop();
          });
        }

        // 购买按钮事件
        if (purchaseBtn) {
          purchaseBtn.addEventListener('click', function() {
            handleShopGiftPurchase();
          });
        }

        // 标签切换事件
        tabs.forEach(tab => {
          tab.addEventListener('click', function() {
            const level = parseInt(this.getAttribute('data-level'));
            updateShopGiftDisplay(level);
            currentShopGiftLevel = level;
          });
        });
      }

      function handleShopGiftPurchase() {
        console.log('商城类型限时礼包购买按钮被点击，等级:', currentShopGiftLevel);
        const giftData = shopGiftTypes[currentShopGiftLevel];
        alert(`购买商城类型限时礼包 Lv.${currentShopGiftLevel}\n价格: ${giftData.price}`);
      }

      window.showLimitedGiftShop = showLimitedGiftShop;
      window.closeLimitedGiftShop = closeLimitedGiftShop;
      window.handleShopGiftPurchase = handleShopGiftPurchase;

      // 灵宠类型限时礼包
      const petGiftTypes = {
        1: { price: '$3.99', image: '/gift-pic/限时礼包/泰/7.png' },
        2: { price: '$7.99', image: '/gift-pic/限时礼包/泰/8.png' },
        3: { price: '$15.99', image: '/gift-pic/限时礼包/泰/9.png' }
      };

      let currentPetGiftLevel = 1;

      function showLimitedGiftPet(level = 1) {
        currentPetGiftLevel = level;
        const petGiftModal = document.getElementById('limited-gift-pet');
        if (petGiftModal) {
          updatePetGiftDisplay(level);
          petGiftModal.style.display = 'flex';
          initPetGift();
        }
      }

      function updatePetGiftDisplay(level) {
        const giftData = petGiftTypes[level];
        if (!giftData) return;

        // 更新背景图片
        const giftContent = document.querySelector('.limited-gift-pet-content');
        if (giftContent) {
          giftContent.style.backgroundImage = `url("${giftData.image}")`;
        }

        // 更新价格
        const priceBtn = document.querySelector('.limited-gift-pet-btn');
        if (priceBtn) {
          priceBtn.textContent = giftData.price;
        }

        // 更新标签状态
        const tabs = document.querySelectorAll('.limited-gift-pet-tab');
        tabs.forEach((tab, index) => {
          tab.classList.remove('active');
          if (index + 1 === level) {
            tab.classList.add('active');
          }
        });
      }

      function closeLimitedGiftPet() {
        const petGiftModal = document.getElementById('limited-gift-pet');
        if (petGiftModal) {
          petGiftModal.style.display = 'none';
        }
      }

      function initPetGift() {
        const closeBtn = document.querySelector('.limited-gift-pet-close');
        const purchaseBtn = document.querySelector('.limited-gift-pet-btn');
        const tabs = document.querySelectorAll('.limited-gift-pet-tab');

        // 关闭按钮事件
        if (closeBtn) {
          closeBtn.addEventListener('click', function() {
            closeLimitedGiftPet();
          });
        }

        // 购买按钮事件
        if (purchaseBtn) {
          purchaseBtn.addEventListener('click', function() {
            handlePetGiftPurchase();
          });
        }

        // 标签切换事件
        tabs.forEach(tab => {
          tab.addEventListener('click', function() {
            const level = parseInt(this.getAttribute('data-level'));
            updatePetGiftDisplay(level);
            currentPetGiftLevel = level;
          });
        });
      }

      function handlePetGiftPurchase() {
        console.log('灵宠类型限时礼包购买按钮被点击，等级:', currentPetGiftLevel);
        const giftData = petGiftTypes[currentPetGiftLevel];
        alert(`购买灵宠类型限时礼包 Lv.${currentPetGiftLevel}\n价格: ${giftData.price}`);
      }

      window.showLimitedGiftPet = showLimitedGiftPet;
      window.closeLimitedGiftPet = closeLimitedGiftPet;
      window.handlePetGiftPurchase = handlePetGiftPurchase;

      // 统一的限时礼包函数
      window.showLimitedGift = showLimitedGift;
    });
  // <!--  ======================================== TY:游戏工具 ============================================-->
})();
</script>
</body>
<!--  ======================================== TY:游戏工具 ============================================-->
<style>
  .game-controls {
    display: none;
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    font-family: 'Arial', sans-serif;
    user-select: none;
  }

  .main-button {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4b79a1, #283e51);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3), inset 0 2px 3px rgba(255, 255, 255, 0.2);
    cursor: grab;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    touch-action: none;
    padding: 0;
  }

  .main-button:active {
    cursor: grabbing;
  }

  .main-button svg {
    width: 24px;
    height: 24px;
    position: relative;
    z-index: 2;
    filter: brightness(0) invert(1);
    display: block;
  }

  .game-menu {
    display: none;
    margin-top: 10px;
    background: rgba(20, 30, 48, 0.95);
    border-radius: 15px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(75, 121, 161, 0.5);
    backdrop-filter: blur(5px);
    transform-origin: bottom left;
    width: 280px;
    overflow: hidden;
  }

  .menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: linear-gradient(to right, rgba(40, 60, 80, 0.9), rgba(60, 80, 100, 0.9));
  }

  .menu-title {
    color: #ffd700;
    font-size: 16px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  }

  .close-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    font-size: 20px;
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    padding: 0;
    transition: all 0.2s;
  }

  .close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
  }

  .menu-tabs {
    display: flex;
    padding: 10px 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .tab-btn {
    flex: 1;
    background: rgba(40, 60, 85, 0.5);
    border: none;
    color: rgba(255, 255, 255, 0.7);
    padding: 8px 0;
    font-size: 14px;
    cursor: pointer;
    border-radius: 8px 8px 0 0;
    transition: all 0.3s;
    border-bottom: 2px solid transparent;
  }

  .tab-btn:hover {
    background: rgba(60, 80, 110, 0.5);
    color: white;
  }

  .tab-btn.active {
    background: rgba(60, 100, 140, 0.6);
    color: #ffd700;
    font-weight: bold;
    border-bottom: 2px solid #ffd700;
  }

  .tab-content {
    padding: 15px;
    max-height: 250px;
    overflow-y: auto;
  }

  .tab-pane {
    display: none;
  }

  .tab-pane.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
  }

  .option-title {
    color: #78a4d4;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
    text-align: center;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
  }

  .option-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
  }

  .option-buttons button, .debug-buttons button {
    background: linear-gradient(to bottom, #5a8bba, #4b79a1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: white;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    outline: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2), inset 0 1px 2px rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
  }

  .option-buttons button:hover, .debug-buttons button:hover {
    background: linear-gradient(to bottom, #6a9bca, #5a8bba);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3), inset 0 1px 2px rgba(255, 255, 255, 0.3);
  }

  .option-buttons button:active, .debug-buttons button:active {
    background: linear-gradient(to bottom, #4b79a1, #3a6891);
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .option-buttons button.active {
    background: linear-gradient(to bottom, #ffa500, #ff8c00);
    box-shadow: 0 0 8px rgba(255, 165, 0, 0.6);
    border: 1px solid #ffd700;
  }

  .debug-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .debug-buttons button {
    text-align: left;
    width: 100%;
    padding: 10px 12px;
  }

  .game-menu.visible {
    display: block;
    animation: slideIn 0.3s ease-in-out forwards;
  }

  @keyframes slideIn {
    from { opacity: 0; transform: translateY(10px) scale(0.9); }
    to { opacity: 1; transform: translateY(0) scale(1); }
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes spinning {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  .item-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px;
  }

  .game-select {
    background: rgba(40, 60, 85, 0.9);
    border: 1px solid rgba(75, 121, 161, 0.5);
    border-radius: 8px;
    color: white;
    padding: 8px 12px;
    font-size: 14px;
    width: 100%;
    cursor: pointer;
    outline: none;
  }

  .game-select option {
    background: #283e51;
    color: white;
  }

  .game-input {
    background: rgba(40, 60, 85, 0.9);
    border: 1px solid rgba(75, 121, 161, 0.5);
    border-radius: 8px;
    color: white;
    padding: 8px 12px;
    font-size: 14px;
    width: 100%;
    outline: none;
    box-sizing: border-box;
  }

  .game-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }

  .game-button {
    background: linear-gradient(to bottom, #5a8bba, #4b79a1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: white;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    outline: none;
    width: 100%;
    transition: all 0.2s ease;
  }

  .game-button:hover {
    background: linear-gradient(to bottom, #6a9bca, #5a8bba);
    transform: translateY(-2px);
  }

  .game-button:active {
    background: linear-gradient(to bottom, #4b79a1, #3a6891);
    transform: translateY(1px);
  }

  .battle-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
    padding: 0 10px;
    box-sizing: border-box;
  }

  .battle-button {
    background: linear-gradient(to bottom, #ff6b6b, #ff4757);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: white;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    outline: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2), inset 0 1px 2px rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
    width: 100%;
  }

  .battle-button:hover {
    background: linear-gradient(to bottom, #ff7b7b, #ff5757);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3), inset 0 1px 2px rgba(255, 255, 255, 0.3);
  }

  .battle-button:active {
    background: linear-gradient(to bottom, #ff5b5b, #ff3737);
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .battle-button.active {
    background: linear-gradient(to bottom, #ffa500, #ff8c00);
    box-shadow: 0 0 8px rgba(255, 165, 0, 0.6);
    border: 1px solid #ffd700;
  }

  /* 多语言选择弹窗样式 */
  .language-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    justify-content: center;
    align-items: center;
  }

  .language-modal.show {
    display: flex;
  }

  .language-modal-content {
    background-image: url('lang-back.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 80vw;
    min-height: 60vh;
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  .language-modal-header {
    background-image: url('lang-title-back.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin-top: 20px;
  }

  .language-modal-title {
    color: #fffBe0;
    font-size: 12px;
    font-weight: bold;
  }

  .language-modal-close {
    position: absolute;
    top: 0;
    right: 0;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
  }

  .language-modal-close img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .language-options {
    padding: 12px 30px 30px;
  }

  .language-option {
    background: transparent;
    padding: 12px 20px;
    cursor: pointer;
    color: #ca9c75;
    transition: all 0.3s ease;
  }

  .language-option.selected {
    background: #ca9c75;
  }

  .language-option.selected .language-name {
    color: #fff;
    font-weight: bold;
  }

    .language-name {
      color: #ca9c75;
      font-size: 16px;
      font-weight: 500;
      text-align: center;
      display: block;
    }

    .delete-account {
        display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 10000;
      justify-content: center;
      align-items: center;
    }

    .delete-account.show {
      display: flex;
    }

    .delete-account-content {
        background-image: url("delete-back.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 80vw;
        height: 45vh;
        min-height: 350px;
        position: relative;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        padding: 0 10%;
    }

    .delete-account-header {
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        margin-top: 30%;
        -webkit-text-stroke: 2px #9D6935;
        paint-order: stroke;
        margin-bottom: 10px;
    }

    .delete-account-title {
        color: #fffbe0;
        font-size: 18px;
        font-weight: bold;
    }

    .delete-account-close {
        position: absolute;
        top: 7%;
        right: 0;
        background: none;
        border: none;
        cursor: pointer;
        padding: 0;
        width: 36px;
        height: 36px;
    }

    .delete-account-close img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    .delete-account-input-bg {
        background-image: url("password.png");
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        width: 100%;
        height: 40px;
        margin-top: 20px;
    }

    .delete-account-input {
        background: transparent;
        border: none;
        outline: none;
        font-size: 14px;
        color: #fffbe0;
        width: 100%;
        height: 100%;
        text-indent: 10px;
        padding-left: 16%;
    }

    .delete-account-input::placeholder {
        color: #D4A778;
    }

    .delete-account-btn {
        background: url("confirm.png") no-repeat center / contain;
        border-radius: 8px;
        color: white;
        border: none;
        outline: none;
        padding: 12px 0;
        font-size: 14px;
        font-weight: bold;
        cursor: pointer;
        height: 10%;
        width: 38%;
        -webkit-text-stroke: 2px #64863B;
        paint-order: stroke;
        margin-top: 40px;
    }
</style>
<!--首充礼包样式-->
<style>
  .first-pay-gift {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    justify-content: center;
    align-items: center;
  }

  .first-pay-gift.show {
    display: flex;
  }

  .first-pay-gift-content {
    background-image: url("/gift-pic/首充礼包/png/中文弹窗底.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    width: 300px;
    aspect-ratio: 1000 / 1070;
    max-width: 350px;
    position: relative;
    border-radius: 15px;
    overflow: hidden;
  }

  .first-pay-gift-options {
    display: flex;
    gap: 20px;
    position: absolute;
    top: 117px;
    left: 15%;
    color: #fff;
  }

  .first-pay-gift-option {
    background: url("/gift-pic/首充礼包/png/常态.png") no-repeat center / contain;
    padding: 20px;
    font-size: 14px;
    cursor: pointer;
    text-align: center;
  }

  .first-pay-gift-option.active {
    background: url("/gift-pic/首充礼包/png/选中.png") no-repeat center / contain;
  }

  .first-pay-gift-option.purchased {
    opacity: 0.7;
    position: relative;
  }

  .first-pay-gift-option.purchased::after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #00ff00;
    font-size: 18px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
  }

  .first-pay-gifts {
    display: flex;
    position: absolute;
    top: 180px;
    left: 70px;
    width: 60%;
    column-gap: 24px;
    justify-content: center;
    align-items: center;
  }

  .first-pay-gift-item {
    background: url("/gift-pic/首充礼包/png/物品缩放/底.png") no-repeat center / contain;
    font-size: 14px;
    cursor: pointer;
    text-align: center;
  }

  .first-pay-gift-item.selected {
    background: url("/gift-pic/首充礼包/png/物品缩放/选中底.png") no-repeat center / contain;
  }

  .first-pay-text {
    position: absolute;
    top: 246px;
    left: 115px;
    color: #fff;
    font-size: 12px;
  }

  .first-pay-gift-btn {
    background: url("/gift-pic/首充礼包/png/充值按钮.png") no-repeat center / cover;
    position: absolute;
    top: 276px;
    left: 105px;
    color: #fff;
    font-size: 16px;
    padding: 6px 32px;
  }

  .first-pay-gift-btn.lock {
    background: url("/gift-pic/首充礼包/png/充值按钮灰态.png") no-repeat center / cover;
  }

  .first-pay-gift-btn.disabled {
    background: url("/gift-pic/首充礼包/png/充值按钮灰态.png") no-repeat center / cover;
  }

  .first-pay-gift-btn.purchased {
    background: url("/gift-pic/首充礼包/png/充值按钮灰态.png") no-repeat center / cover;
    color: #888;
  }

  .first-pay-gift-close {
    position: absolute;
    top: 20%;
    right: 0;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
  }

  .first-pay-gift-close img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
</style>
<!--体力补充礼包样式-->
<style>
  .power-gift {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    justify-content: center;
    align-items: center;
  }

  .power-gift.show {
    display: flex;
  }

  .power-gift-content {
    background-image: url("/gift-pic/体力补充包/png/体力泰.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    width: 100%;
    aspect-ratio: 1080 / 984;
    /*max-width: 350px;*/
    position: relative;
  }

  .power-gift-close {
    position: absolute;
    top: 20%;
    right: 16px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
  }

  .power-gift-close img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .power-gift-btn {
    background: url("/gift-pic/体力补充包/png/按钮.png") no-repeat center / cover;
    position: absolute;
    bottom: -5%;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    font-size: 16px;
    padding: 6px 32px;
    border: none;
    cursor: pointer;
    font-weight: bold;
  }
</style>
<!--能力提升礼包样式-->
<style>
  .ability-gift {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    justify-content: center;
    align-items: center;
  }

  .ability-gift.show {
    display: flex;
  }

  .ability-gift-content {
    background-image: url("/gift-pic/能力提升/png/泰弹窗.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    width: 100%;
    aspect-ratio: 1080 / 984;
    /*max-width: 350px;*/
    position: relative;
  }

  .ability-gift-close {
    position: absolute;
    top: 20%;
    right: 16px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
  }

  .ability-gift-close img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .ability-gift-btn {
    background: url("/gift-pic/能力提升/png/充值按钮.png") no-repeat center / cover;
    position: absolute;
    bottom: -5%;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    font-size: 16px;
    padding: 6px 32px;
    border: none;
    cursor: pointer;
    font-weight: bold;
  }
</style>

<!--限时礼包通用样式-->
<style>
  /* 限时礼包弹窗基础样式 */
  .limited-gift-skill,
  .limited-gift-shop,
  .limited-gift-pet {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    justify-content: center;
    align-items: center;
  }

  .limited-gift-skill.show,
  .limited-gift-shop.show,
  .limited-gift-pet.show {
    display: flex;
  }

  /* 限时礼包内容区域 */
  .limited-gift-skill-content,
  .limited-gift-shop-content,
  .limited-gift-pet-content {
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    width: 100%;
    aspect-ratio: 1067 / 1144;
    position: relative;
  }

  /* 技能类型默认背景 */
  .limited-gift-skill-content {
    background-image: url("/gift-pic/限时礼包/泰/1.png");
  }

  /* 商城类型默认背景 */
  .limited-gift-shop-content {
    background-image: url("/gift-pic/限时礼包/泰/4.png");
  }

  /* 灵宠类型默认背景 */
  .limited-gift-pet-content {
    background-image: url("/gift-pic/限时礼包/泰/7.png");
  }

  /* 关闭按钮通用样式 */
  .limited-gift-skill-close,
  .limited-gift-shop-close,
  .limited-gift-pet-close {
    position: absolute;
    top: 20%;
    right: 16px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
  }

  .limited-gift-skill-close img,
  .limited-gift-shop-close img,
  .limited-gift-pet-close img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  /* 标签栏容器通用样式 */
  .limited-gift-skill-tabs,
  .limited-gift-shop-tabs,
  .limited-gift-pet-tabs {
    position: absolute;
    top: 50%;
    right: 1%;
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
  }

  /* 标签按钮通用样式 */
  .limited-gift-skill-tab,
  .limited-gift-shop-tab,
  .limited-gift-pet-tab {
    background: url("/gift-pic/限时礼包/png/常态.png") no-repeat center / contain;
    width: 60px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .limited-gift-skill-tab.active,
  .limited-gift-shop-tab.active,
  .limited-gift-pet-tab.active {
    background: url("/gift-pic/限时礼包/png/选中.png") no-repeat center / contain;
  }

  /* 购买按钮通用样式 */
  .limited-gift-skill-btn,
  .limited-gift-shop-btn,
  .limited-gift-pet-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: url("/gift-pic/限时礼包/png/充值按钮.png") no-repeat center / cover;
    position: absolute;
    bottom: 1%;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    font-size: 16px;
    width: 128px;
    aspect-ratio: 390 / 130;
    font-weight: bold;
    cursor: pointer;
    border: none;
  }
</style>


  <!--  ======================================== TY:游戏工具 ============================================-->
</html>
