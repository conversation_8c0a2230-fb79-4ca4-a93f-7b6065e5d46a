{"creative": {"channelCode": "tafang-internal", "channelName": "塔防-内网", "createBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createTime": "2023-11-07 10:26:38", "creativeId": "tafang-internal-0", "extra": {"isOpenLog": "1"}, "gameConfigId": "657310efd1a1fc59765ba494", "id": "6549a05ed1a1fc3b280a33e7", "pid": "6549a03ad1a1fc3b280a33df", "pids": ["0", "6549a03ad1a1fc3b280a33df"], "projectId": 26, "rootChannelCode": "tafang-0", "rootChannelName": "塔防-大渠道", "title": "塔防-内网", "type": 2, "updateBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateTime": "2023-12-26 17:10:12", "versionId": "658a9853d1a1fc59765babed"}, "gameConfig": {"isShowFriendCloudRank": false, "channel": "tafang-internal", "sdkType": "YouaiSDK", "language": "zh-cn", "apkUrl": "http://localhost", "audit2": false, "lastFullUpdateVersion2": "0.0.0", "backstageCode": "26", "isForceUpdateApp": false, "audit": false, "id": "657310efd1a1fc59765ba494", "whTips": "服务器维护中，请稍后再试试。。", "TY:游戏配置2": "请求连接修改", "backstageurl": "https://xian-test.uggamer.com/", "fenghaoTips": "您的账号已被禁止登录，请联系客服QQ：12345。", "channelCode": 0, "ext": [{"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "渠道标识", "type": "string", "value": "tafang-internal", "key": "channel"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "渠道名", "type": "string", "value": "塔防-内网-3", "key": "name"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "trackType", "type": "string", "value": "yes", "key": "trackType"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "中心服地址", "type": "string", "TY:游戏配置2": "请求连接修改", "value": "https://xian-test.uggamer.com/:9583", "key": "url"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "后台地址", "type": "string", "value": "https://emr-daqin-diguo.oss-cn-shanghai.aliyuncs.com/file-up/test/gmbackend-daqin2", "key": "<PERSON><PERSON><PERSON>"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "SDK", "type": "string", "value": "YouaiSDK", "key": "sdkType"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "版本", "type": "string", "value": "0.0.1", "key": "version"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "大版本号", "type": "string", "value": "1", "key": "versionCode"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "提审服", "type": "boolean", "value": false, "key": "audit"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "调试模式", "type": "string", "value": "0", "key": "edit"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "渠道Code", "type": "number", "value": 0, "key": "channelCode"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "是否WSS", "type": "boolean", "value": false, "key": "isWss"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "游戏ID报点用", "type": "number", "value": 0, "key": "gameId"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "版号信息", "type": "string", "value": "审批文号：新广出审[2018]1427号 出版单位：北京科海电子出版社 \\n著作权人：广州炫动信息科技有限公司 出版物号：ISBN 978-7-498-04775-5", "key": "plateInfo"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "apk下载地址", "type": "string", "value": "http://localhost", "key": "apkUrl"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "提审服地址", "type": "string", "value": "http://*************:7503", "key": "tsUrl"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "开发服地址", "type": "string", "key": "devUrl"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "维护状态", "type": "boolean", "value": false, "key": "isMaintenance"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "维护提示", "type": "string", "value": "服务器维护中，请稍后再试试。。", "key": "whTips"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "隐私协议", "type": "string", "key": "privacyAgreement"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "用户协议", "type": "string", "key": "userAgreement"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "支付回调", "type": "string", "key": "payUrl"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "日志开关", "type": "string", "value": "1", "key": "isOpenLog"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "封号提示", "type": "string", "value": "您的账号已被禁止登录，请联系客服QQ：12345。", "key": "feng<PERSON><PERSON><PERSON>s"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "好友排行榜开关", "type": "boolean", "value": false, "key": "isShowFriendCloudRank"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "语言", "type": "string", "value": "zh-cn", "key": "language"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "热更地址", "type": "string", "key": "hotUpdateUrl"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "整包更新版本号", "type": "string", "key": "lastFullUpdateVersion"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "后台地址项目ID", "type": "string", "value": "26", "key": "backstageCode"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "版本更新提醒", "type": "string", "key": "releaseVersion"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "提审服2", "type": "boolean", "value": false, "key": "audit2"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "中心服地址HTTP", "type": "string", "key": "urlHTTP"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "渠道用商品名", "type": "string", "key": "payProductNameField"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "热更版本", "type": "string", "key": "resUpdateVersion"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "热更版本测试", "type": "string", "key": "resUpdateVersionTest"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "热更版本测试", "type": "string", "key": "versionTest"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "强更提示文本", "type": "string", "value": "发现新版本，请跳转下载。", "key": "updateAppTips"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "强更包体版本", "type": "string", "value": "0.0.0", "key": "lastFullUpdateVersion2"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "是否强更", "type": "boolean", "value": false, "key": "isForceUpdateApp"}], "gameId": 0, "trackType": "yes", "edit": "0", "tsUrl": "http://*************:7503", "updateTime": 1704877915204, "isMaintenance": false, "version": "0.0.1", "TY:游戏配置3": "请求连接修改", "url": "https://xian-test.uggamer.com/:9583/", "versionCode": "1", "TY:游戏配置": "ws连接修改", "isWss": true, "createBy": "xiezekai", "updateAppTips": "发现新版本，请跳转下载。", "plateInfo": "审批文号：新广出审[2018]1427号 出版单位：北京科海电子出版社 \\n著作权人：广州炫动信息科技有限公司 出版物号：ISBN 978-7-498-04775-5", "createTime": 1702039791906, "extConsts": {"isShowFriendCloudRank": false, "channel": "tafang-internal", "sdkType": "YouaiSDK", "language": "zh-cn", "apkUrl": "http://localhost", "audit2": false, "lastFullUpdateVersion2": "0.0.0", "backstageCode": "26", "isForceUpdateApp": false, "audit": false, "whTips": "服务器维护中，请稍后再试试。。", "TY:游戏配置2": "请求连接修改", "backstageurl": "https://xian-test.uggamer.com//", "fenghaoTips": "您的账号已被禁止登录，请联系客服QQ：12345。", "channelCode": 0, "gameId": 0, "trackType": "yes", "edit": "0", "tsUrl": "http://*************:7503", "isMaintenance": false, "version": "0.0.1", "TY:游戏配置3": "请求连接修改", "url": "https://xian-test.uggamer.com/:9583/", "versionCode": "1", "TY:游戏配置": "ws连接修改", "isWss": true, "updateAppTips": "发现新版本，请跳转下载。", "plateInfo": "审批文号：新广出审[2018]1427号 出版单位：北京科海电子出版社 \\n著作权人：广州炫动信息科技有限公司 出版物号：ISBN 978-7-498-04775-5", "isOpenLog": "1", "name": "塔防-内网-3"}, "isOpenLog": "1", "name": "塔防-内网-3", "bgName": "塔防-内网-3", "tplId": "6568564eca53cf7e73c7bae2", "projectId": 26}, "versionConfig": {"ext": [{"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "版本名", "type": "string", "value": "0.1.0.1_20231226_d1", "key": "VersionName"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "版本号", "type": "string", "value": "*******", "key": "version"}, {"isDeleted": 1, "isSearch": 0, "visiable": 1, "title": "大版本号", "type": "string", "value": "1", "key": "versionCode"}], "createBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createTime": 1703581779591, "extConsts": {"VersionName": "0.1.0.1_20231226_d1", "version": "*******", "versionCode": "1"}, "updateTime": 1703581962021, "VersionName": "0.1.0.1_20231226_d1", "bgName": "塔防-内网-版本", "id": "658a9853d1a1fc59765babed", "tplId": "6568564eca53cf7e73c7bae3", "projectId": 26, "version": "*******", "versionCode": "1"}}